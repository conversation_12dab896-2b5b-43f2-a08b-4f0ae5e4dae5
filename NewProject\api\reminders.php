<?php
/**
 * Reminders API Endpoints
 * RESTful API for reminder management
 */

require_once '../config/config.php';

$method = $_SERVER['REQUEST_METHOD'];
$request_uri = $_SERVER['REQUEST_URI'];
$path_parts = explode('/', trim(parse_url($request_uri, PHP_URL_PATH), '/'));

// Get reminder ID if provided
$reminder_id = isset($path_parts[2]) && is_numeric($path_parts[2]) ? (int)$path_parts[2] : null;

switch ($method) {
    case 'GET':
        if ($reminder_id) {
            getReminder($reminder_id);
        } else {
            getReminders();
        }
        break;
    
    case 'POST':
        createReminder();
        break;
    
    case 'PUT':
        if ($reminder_id) {
            updateReminder($reminder_id);
        } else {
            jsonResponse(['error' => 'Reminder ID is required'], 400);
        }
        break;
    
    case 'DELETE':
        if ($reminder_id) {
            deleteReminder($reminder_id);
        } else {
            jsonResponse(['error' => 'Reminder ID is required'], 400);
        }
        break;
    
    default:
        jsonResponse(['error' => 'Method not allowed'], 405);
        break;
}

function getReminders() {
    requireAuth();
    
    $database = new Database();
    $db = $database->getConnection();
    $reminder = new Reminder($db);
    
    // Check if admin wants to see all reminders
    if (isAdmin() && isset($_GET['all']) && $_GET['all'] === 'true') {
        $stmt = $reminder->readAll();
    } else {
        $stmt = $reminder->readByUser($_SESSION['user_id']);
    }
    
    $reminders = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $reminders[] = $row;
    }
    
    jsonResponse([
        'success' => true,
        'reminders' => $reminders,
        'count' => count($reminders)
    ]);
}

function getReminder($reminder_id) {
    requireAuth();
    
    $database = new Database();
    $db = $database->getConnection();
    $reminder = new Reminder($db);
    $reminder->id = $reminder_id;
    
    if ($reminder->readOne()) {
        // Check if user owns this reminder or is admin
        if ($reminder->user_id != $_SESSION['user_id'] && !isAdmin()) {
            jsonResponse(['error' => 'Access denied'], 403);
        }
        
        jsonResponse([
            'success' => true,
            'reminder' => [
                'id' => $reminder->id,
                'user_id' => $reminder->user_id,
                'task_id' => $reminder->task_id,
                'title' => $reminder->title,
                'description' => $reminder->description,
                'reminder_time' => $reminder->reminder_time,
                'is_recurring' => $reminder->is_recurring,
                'recurrence_pattern' => $reminder->recurrence_pattern,
                'status' => $reminder->status,
                'created_at' => $reminder->created_at,
                'updated_at' => $reminder->updated_at
            ]
        ]);
    } else {
        jsonResponse(['error' => 'Reminder not found'], 404);
    }
}

function createReminder() {
    requireAuth();
    
    $data = json_decode(file_get_contents("php://input"), true);
    
    // Validate required fields
    if (empty($data['title'])) {
        jsonResponse(['error' => 'Title is required'], 400);
    }
    
    if (empty($data['reminder_time'])) {
        jsonResponse(['error' => 'Reminder time is required'], 400);
    }
    
    // Validate reminder_time format
    $date = DateTime::createFromFormat('Y-m-d H:i:s', $data['reminder_time']);
    if (!$date) {
        jsonResponse(['error' => 'Invalid reminder_time format. Use: YYYY-MM-DD HH:MM:SS'], 400);
    }
    
    // Validate status
    $valid_statuses = ['active', 'completed', 'cancelled'];
    if (isset($data['status']) && !in_array($data['status'], $valid_statuses)) {
        jsonResponse(['error' => 'Invalid status. Must be: active, completed, or cancelled'], 400);
    }
    
    // Validate recurrence pattern
    $valid_patterns = ['daily', 'weekly', 'monthly'];
    if (isset($data['recurrence_pattern']) && !empty($data['recurrence_pattern']) && !in_array($data['recurrence_pattern'], $valid_patterns)) {
        jsonResponse(['error' => 'Invalid recurrence pattern. Must be: daily, weekly, or monthly'], 400);
    }
    
    // Validate task_id if provided
    if (isset($data['task_id']) && !empty($data['task_id'])) {
        $database = new Database();
        $db = $database->getConnection();
        $task = new Task($db);
        $task->id = $data['task_id'];
        
        if (!$task->readOne()) {
            jsonResponse(['error' => 'Invalid task ID'], 400);
        }
        
        // Check if user owns the task
        if ($task->user_id != $_SESSION['user_id'] && !isAdmin()) {
            jsonResponse(['error' => 'Access denied to the specified task'], 403);
        }
    }
    
    $database = new Database();
    $db = $database->getConnection();
    $reminder = new Reminder($db);
    
    // Set reminder properties
    $reminder->user_id = $_SESSION['user_id'];
    $reminder->task_id = $data['task_id'] ?? null;
    $reminder->title = $data['title'];
    $reminder->description = $data['description'] ?? '';
    $reminder->reminder_time = $data['reminder_time'];
    $reminder->is_recurring = isset($data['is_recurring']) ? (bool)$data['is_recurring'] : false;
    $reminder->recurrence_pattern = $data['recurrence_pattern'] ?? null;
    $reminder->status = $data['status'] ?? 'active';
    
    if ($reminder->create()) {
        jsonResponse([
            'success' => true,
            'message' => 'Reminder created successfully',
            'reminder_id' => $reminder->id
        ], 201);
    } else {
        jsonResponse(['error' => 'Failed to create reminder'], 500);
    }
}

function updateReminder($reminder_id) {
    requireAuth();
    
    $data = json_decode(file_get_contents("php://input"), true);
    
    $database = new Database();
    $db = $database->getConnection();
    $reminder = new Reminder($db);
    $reminder->id = $reminder_id;
    
    if (!$reminder->readOne()) {
        jsonResponse(['error' => 'Reminder not found'], 404);
    }
    
    // Check if user owns this reminder or is admin
    if ($reminder->user_id != $_SESSION['user_id'] && !isAdmin()) {
        jsonResponse(['error' => 'Access denied'], 403);
    }
    
    // Validate reminder_time format if provided
    if (isset($data['reminder_time']) && !empty($data['reminder_time'])) {
        $date = DateTime::createFromFormat('Y-m-d H:i:s', $data['reminder_time']);
        if (!$date) {
            jsonResponse(['error' => 'Invalid reminder_time format. Use: YYYY-MM-DD HH:MM:SS'], 400);
        }
    }
    
    // Validate status if provided
    if (isset($data['status'])) {
        $valid_statuses = ['active', 'completed', 'cancelled'];
        if (!in_array($data['status'], $valid_statuses)) {
            jsonResponse(['error' => 'Invalid status. Must be: active, completed, or cancelled'], 400);
        }
    }
    
    // Validate recurrence pattern if provided
    if (isset($data['recurrence_pattern']) && !empty($data['recurrence_pattern'])) {
        $valid_patterns = ['daily', 'weekly', 'monthly'];
        if (!in_array($data['recurrence_pattern'], $valid_patterns)) {
            jsonResponse(['error' => 'Invalid recurrence pattern. Must be: daily, weekly, or monthly'], 400);
        }
    }
    
    // Validate task_id if provided
    if (isset($data['task_id']) && !empty($data['task_id'])) {
        $task = new Task($db);
        $task->id = $data['task_id'];
        
        if (!$task->readOne()) {
            jsonResponse(['error' => 'Invalid task ID'], 400);
        }
        
        // Check if user owns the task
        if ($task->user_id != $_SESSION['user_id'] && !isAdmin()) {
            jsonResponse(['error' => 'Access denied to the specified task'], 403);
        }
    }
    
    // Update reminder properties
    $reminder->task_id = isset($data['task_id']) ? $data['task_id'] : $reminder->task_id;
    $reminder->title = $data['title'] ?? $reminder->title;
    $reminder->description = $data['description'] ?? $reminder->description;
    $reminder->reminder_time = $data['reminder_time'] ?? $reminder->reminder_time;
    $reminder->is_recurring = isset($data['is_recurring']) ? (bool)$data['is_recurring'] : $reminder->is_recurring;
    $reminder->recurrence_pattern = isset($data['recurrence_pattern']) ? $data['recurrence_pattern'] : $reminder->recurrence_pattern;
    $reminder->status = $data['status'] ?? $reminder->status;
    
    if ($reminder->update()) {
        jsonResponse([
            'success' => true,
            'message' => 'Reminder updated successfully'
        ]);
    } else {
        jsonResponse(['error' => 'Failed to update reminder'], 500);
    }
}

function deleteReminder($reminder_id) {
    requireAuth();
    
    $database = new Database();
    $db = $database->getConnection();
    $reminder = new Reminder($db);
    $reminder->id = $reminder_id;
    
    if (!$reminder->readOne()) {
        jsonResponse(['error' => 'Reminder not found'], 404);
    }
    
    // Check if user owns this reminder or is admin
    if ($reminder->user_id != $_SESSION['user_id'] && !isAdmin()) {
        jsonResponse(['error' => 'Access denied'], 403);
    }
    
    if ($reminder->delete()) {
        jsonResponse([
            'success' => true,
            'message' => 'Reminder deleted successfully'
        ]);
    } else {
        jsonResponse(['error' => 'Failed to delete reminder'], 500);
    }
}
?>
