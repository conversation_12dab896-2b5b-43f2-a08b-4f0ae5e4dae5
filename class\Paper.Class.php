<?php
class Paper extends SQLQuery{
	public $table = 'paper';
	// for paper file functions
	public function add_paper($post){
		$post['user_id']=$_SESSION['user_id'];
		$query = parent::insert_record($this->table,$post);
		$data = parent::update_query($query);
	}
	public function update_paper($post){
		$query = parent::update_record($this->table,$post,$post['id']);
		$data = parent::update_query($query);
	}
	public function delete_paper($id){
		$query = parent::delete_record($this->table,$id);
		$result = parent::update_query($query);
	}
	public function view_paper_all(){
		$query = parent::select_table($this->table);		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function view_paper_add_by_user($user_id){
		$query = "SELECT * FROM `paper` WHERE `user_id` =".$user_id;
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function select_paper_id($id){
		$query = parent::select_record($this->table,$id);		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	/*used in testlist.php for listd all paper fot the standard
	public function view_user_standard_paper_all(){
		$query = "SELECT DISTINCT `paper`.`name`, `paper`.`id` AS `id`, `standard`.`name` AS`standard_name` FROM `user_topic` JOIN `standard` JOIN `paperset` JOIN `paper` ON `user_topic`.`standard_id`=`paperset`.`standard_id` AND `paperset`.`paper_id`=`paper`.`id` AND `paperset`.`standard_id`=`standard`.`id` WHERE `user_topic`.`user_id`=".$_SESSION['user_id'];
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	} */
	public function view_user_standard_subject_paper($user_id,$standard_id,$subject_id){	
		$query = "SELECT DISTINCT `paper`.*	FROM `user_topic` JOIN `standard` JOIN `paperset` JOIN `paper` 	ON `user_topic`.`standard_id`=`paperset`.`standard_id` AND `paperset`.`standard_id`=`standard`.`id` AND `paperset`.`paper_id`=`paper`.`id` WHERE `paper`.`status`='Public' AND `paperset`.`standard_id`=".$standard_id." AND `paperset`.`subject_id`=".$subject_id." AND `user_topic`.`user_id`=".$user_id." UNION SELECT DISTINCT `paper`.* FROM `user_topic` JOIN `user_supervisor` JOIN `standard` JOIN `paperset` JOIN `paper`ON `user_topic`.`standard_id`=`paperset`.`standard_id` AND `paperset`.`standard_id`=`standard`.`id` AND `paperset`.`paper_id`=`paper`.`id` AND `user_supervisor`.`supervisor_id`=`paper`.`user_id` WHERE `paper`.`status`='Private' AND `user_supervisor`.`user_id`=".$user_id." AND `user_topic`.`user_id`=".$user_id." AND `paperset`.`standard_id`=".$standard_id." AND `paperset`.`subject_id`=".$subject_id;
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	//used in exam.php get user name
	public function paper_author($id){
		$query = "SELECT `user`.`name` AS `author_name`, `paper`.`time` AS `paper_time` FROM `paper` JOIN `user` ON `user`.`id`=`paper`.`user_id`AND `paper`.`id`=".$id;		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array[0];
	}
}
?>