﻿<?php include("bootfile.php"); 
	if(!$user_ob->islogin()){
		$_SESSION['user_id'] ="2"; $_SESSION['user_type']="user"; }
	$user_ob->user_page();
	$question_class = CLASS_PATH.'Question.Class.php';	require_once((string)$question_class);	unset($question_class);
	$question_ob = new Question();
	$paperset_class = CLASS_PATH.'PaperSet.Class.php';	require_once((string)$paperset_class);	unset($paperset_class);
	$paperset_ob = new PaperSet();

	$paper_class = CLASS_PATH.'Paper.Class.php';	require_once((string)$paper_class);	unset($paper_class);
	$paper_ob = new Paper();

	$result_class = CLASS_PATH.'Result.Class.php';	require_once((string)$result_class);	unset($result_class);
	$result_ob = new Result();
	if(!empty($_POST)){	$result_ob->add_result($_POST); }

	$paper_id=0; if(isset($_REQUEST['paperid'])){ $paper_id=$_REQUEST['paperid']; } 
	$paper_author_and_time = $paper_ob->paper_author($paper_id);
	$rows = $paperset_ob->select_paperset_all_questions($paper_id); $k=0; $total_que = count($rows); ?>
<!DOCTYPE html>
<html lang="en"> 
<head>
  <title>PreTest Paper prepared By <?=$paper_author_and_time['author_name']?></title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
  <style>  p{ margin: 0;  } </style>
</head>
<body>
  <nav class="navbar sticky-top navbar-expand-md bg-light navbar-light">
    <div class="container-fluid">      
        <div class="h2"><span style="color:black"><?=$paper_author_and_time['author_name']?>'s</span> <span style="color:orange">Pre</span><span style="color:black">Test</span></div>
		<button type="button" class="btn btn-outline-dark"><span id="time" class="strong" style="color: red;">Time</span></button>
		
		
      </div>
  </nav>
<div class="container mt-3">	
	<h2> <?php if(isset($rows[0]['name'])){ echo$rows[0]['name']; } else { header('location:index.php'); } ?> </h2>
	<form action="#" method="POST" id="exam_form" >
		<div id="accordion">
			<div class="d-flex flex-row-reverse"></div>
			<?php foreach($rows as $row){ $k++; ?>
			<div id="collapse<?=$k?>" class="collapse <?php if($k==1){ echo 'show'; }  ?>" data-bs-parent="#accordion">
				<div class="card">
					<div class="card-body">
						<h4 class="card-title">Question: <?=$k?> </h4>
						<p class="card-text"><?=$row["que"]?></p><p class="pb-3"></p>
						<?php if($row["opt1"]!=""){ ?>
							<div class="list-group-item">
							<label class="form-check" for="1_<?=$row["id"]?>"><input class="form-check-input" name="que<?=$row["id"]?>" type="radio" value="<?=$row["opt1"]?>" id="1_<?=$row["id"]?>" ><?=$row["opt1"]?></label>
							</div>
						<?php } if($row["opt2"]!=""){ ?>
							<div class="list-group-item">
							<label class="form-check" for="2_<?=$row["id"]?>"><input class="form-check-input" name="que<?=$row["id"]?>" type="radio" value="<?=$row["opt2"]?>" id="2_<?=$row["id"]?>" ><?=$row["opt2"]?></label>
							</div>
						<?php } if($row["opt3"]!=""){ ?>
							<div class="list-group-item">
							<label class="form-check" for="3_<?=$row["id"]?>"><input class="form-check-input" name="que<?=$row["id"]?>" type="radio" value="<?=$row["opt3"]?>" id="3_<?=$row["id"]?>" ><?=$row["opt3"]?></label>
							</div>
						<?php } if($row["opt4"]!=""){ ?>
							<div class="list-group-item">
							<label class="form-check" for="4_<?=$row["id"]?>"><input class="form-check-input" name="que<?=$row["id"]?>" type="radio" value="<?=$row["opt4"]?>" id="4_<?=$row["id"]?>" ><?=$row["opt4"]?></label>
							</div>
						<?php } if($row["opt5"]!=""){ ?>
							<div class="list-group-item">
							<label class="form-check" for="5_<?=$row["id"]?>"><input class="form-check-input" name="que<?=$row["id"]?>" type="radio" value="<?=$row["opt5"]?>" id="5_<?=$row["id"]?>" ><?=$row["opt5"]?></label>
							</div>
						<?php } ?>
					</div>
				</div>
			</div>
			<?php } ?>			
		</div>		
		<input type="hidden" name="paper_id" value="<?=$_REQUEST['paperid']?>" />
		<?php date_default_timezone_set("Asia/Kolkata"); ?>
		<input type="hidden" name="start_time" value="<?=date('Y-m-d H:i:s')?>" />
		<div class="d-flex justify-content-end">
			<div class="btn-group mt-3 me-5">
				<button id="prev_btn" type="button" onclick="prev_que()" class="btn btn-info float-right fw-bold me-2" data-bs-toggle="collapse" data-bs-target="#collapse1"onclick="previous_que()"> <i class='fa fa-angle-double-left'></i> Previous &nbsp; </button>&nbsp;
				<button id="next_btn" type="button" onclick="next_que()" class="btn btn-info float-right fw-bold" data-bs-toggle="collapse" data-bs-target="#collapse2"> &nbsp; &nbsp; Next <i class='fa fa-angle-double-right'></i> &nbsp; &nbsp; </button>
			</div>
		</div>
		<div class="d-flex justify-content-between mt-3 table-responsive">
			<?php for($i=1; $i<=$total_que; $i++){ ?>
			<div class="p">
				<button onclick="my_btn_val(<?=$i?>)" id="que_btn_<?=$i?>" class="page-link" data-bs-toggle="collapse" data-bs-target="#collapse<?=$i?>"><?=$i?></button>
				<?php if($i%10==0){ echo "<br />"; } ?>
			</div>
			<?php } ?>
		</div> 
		<div class="btn-group mt-3">
			<button type="button" onclick="count_attemt_que()" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#conformexam">Submit</button>
		</div>
	</form>
</div>

<!-- The Modal -->
<div class="modal" id="conformexam">
  <div class="modal-dialog">
    <div class="modal-content">
      <!-- Modal Header -->
      <div class="modal-header">
        <h4 class="modal-title">Are you sure submit ?</h4>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <!-- Modal body -->
      <div class="modal-body">
        Total Attend Questions : <label id="Total_attend_que"></label><br/>
		Not Attend Questions   : <label id="Not_attend_que"></label><br/>
      </div>
      <!-- Modal footer -->
      <div class="modal-footer">
        <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
		<button type="button" onclick="exam_form_submit()" class="btn btn-success" data-bs-dismiss="modal">Submit</button>
      </div>
    </div>
  </div>
</div>

<script>
var total_que = <?=$total_que?>;
function exam_form_submit(){	
	document.getElementById("exam_form").submit(); 
}
function count_attemt_que(){	
	var radios = document.getElementsByTagName('input');
	var countQue=0;
	for (var i = 0; i < radios.length; i++) {
    	if (radios[i].type === 'radio' && radios[i].checked) {        
        	countQue++; }  }
	document.getElementById('Total_attend_que').innerHTML=countQue;
	document.getElementById('Not_attend_que').innerHTML=(total_que-countQue);
}
var next_prev_btn=2;
document.getElementById("que_btn_"+(next_prev_btn-1)).setAttribute("disabled", true);
function my_btn_val(val){
	next_prev_btn = val;
	document.getElementById("next_btn").setAttribute("data-bs-target", '#collapse' + (next_prev_btn+1));
	document.getElementById("prev_btn").setAttribute("data-bs-target", '#collapse' + (next_prev_btn-1));
	next_prev_btn = val+1;
	for(var k=1; k<=total_que; k++){ document.getElementById("que_btn_"+(k)).removeAttribute("disabled"); }	
	document.getElementById("que_btn_"+(next_prev_btn-1)).setAttribute("disabled", true);	
	
}
function next_que(){
	if(next_prev_btn<=total_que){ next_prev_btn++; }
	document.getElementById("next_btn").setAttribute("data-bs-target", '#collapse' + next_prev_btn);
	document.getElementById("prev_btn").setAttribute("data-bs-target", '#collapse' + (next_prev_btn-2));
	for(var k=1; k<=total_que; k++){ document.getElementById("que_btn_"+(k)).removeAttribute("disabled"); }	
	document.getElementById("que_btn_"+(next_prev_btn-1)).setAttribute("disabled", true);	
}
function prev_que(){
	if(next_prev_btn>2){ next_prev_btn--; }
	document.getElementById("prev_btn").setAttribute("data-bs-target", '#collapse' + (next_prev_btn-2));
	document.getElementById("next_btn").setAttribute("data-bs-target", '#collapse' + next_prev_btn);
	for(var k=1; k<=total_que; k++){ document.getElementById("que_btn_"+(k)).removeAttribute("disabled"); }	
	document.getElementById("que_btn_"+(next_prev_btn-1)).setAttribute("disabled", true);	
}

function startTimer(duration, display) {
    var timer = duration, minutes, seconds;
    setInterval(function () {
        minutes = parseInt(timer / 60, 10);
        seconds = parseInt(timer % 60, 10);

        minutes = minutes < 10 ? "0" + minutes : minutes;
        seconds = seconds < 10 ? "0" + seconds : seconds;

        display.textContent = minutes + ":" + seconds;

        if (--timer < 0) {
            exam_form_submit();
        }
    }, 1000);
}
window.onload = function () {
    var duration = 60 * <?=$paper_author_and_time['paper_time']?>,
        display = document.querySelector('#time');
    startTimer(duration, display);
};
</script>

<nav class="navbar fixed-bottom justify-content-center">
  <div class="small fst-italic">
    Copyright © 2022 <u style="color: blue;">RekTech</u>. All rights reserved.
  </div>
</nav>
</body>
</html>
