# Quick Setup Guide

## 🚀 Getting Started

### Step 1: Database Setup
1. Create a MySQL database named `task_reminder_app`
2. Import the schema:
   ```sql
   mysql -u your_username -p task_reminder_app < database/schema.sql
   ```

### Step 2: Configure Database Connection
Edit `config/database.php` and update your database credentials:
```php
private $host = 'localhost';
private $db_name = 'task_reminder_app';
private $username = 'your_db_username';
private $password = 'your_db_password';
```

### Step 3: Test the Setup
1. Open your browser and go to: `http://your-domain/NewProject/test-api.php`
2. This will test all components and show you any issues

### Step 4: Access the Application
- **Landing Page**: `http://your-domain/NewProject/index.php`
- **Login**: `http://your-domain/NewProject/views/login.html`

### Default Credentials
- **Admin**: username: `admin`, password: `password`
- **User**: username: `john_doe`, password: `password`

## 🔧 Troubleshooting

### Common Issues:

1. **"Network error" on login**:
   - Check if `test-api.php` shows all green checkmarks
   - Verify database connection in `config/database.php`
   - Ensure web server has PHP enabled

2. **Database connection failed**:
   - Verify MySQL is running
   - Check database credentials
   - Ensure database `task_reminder_app` exists

3. **API endpoints not working**:
   - Try direct endpoints: `/api/login.php` instead of `/api/auth/login`
   - Check if `.htaccess` is supported by your server

4. **Permission errors**:
   - Ensure web server can read all files
   - Check PHP session directory permissions

### Quick Test Commands:

```bash
# Test database connection
mysql -u your_username -p -e "USE task_reminder_app; SHOW TABLES;"

# Check PHP version (requires PHP 7.4+)
php -v

# Test file permissions
ls -la NewProject/
```

## 📁 File Structure
```
NewProject/
├── api/
│   ├── login.php          # Direct login endpoint
│   ├── register.php       # Direct register endpoint
│   └── [other APIs]       # Full API endpoints
├── config/
│   ├── database.php       # Database configuration
│   └── config.php         # Main configuration
├── views/
│   ├── login.html         # Login page
│   └── [other views]      # Other pages
├── test-api.php           # Test all components
└── index.php              # Landing page
```

## 🎯 Next Steps
1. Run `test-api.php` to verify everything works
2. Try logging in with default credentials
3. Create new users and test functionality
4. Customize the application as needed

## 💡 Tips
- Always test with `test-api.php` first
- Check browser console for JavaScript errors
- Use browser developer tools to inspect network requests
- Check PHP error logs if issues persist
