<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - TaskManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.2);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .task-priority-high {
            border-left: 4px solid #dc3545;
        }
        .task-priority-medium {
            border-left: 4px solid #ffc107;
        }
        .task-priority-low {
            border-left: 4px solid #28a745;
        }
        .btn-custom {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            border-radius: 25px;
        }
        .btn-custom:hover {
            color: white;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <i class="fas fa-tasks fa-2x text-white mb-2"></i>
                        <h5 class="text-white">TaskManager</h5>
                        <small class="text-white-50" id="userWelcome">Welcome!</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" data-section="overview">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="tasks">
                                <i class="fas fa-tasks me-2"></i>Tasks
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="reminders">
                                <i class="fas fa-bell me-2"></i>Reminders
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="profile">
                                <i class="fas fa-user me-2"></i>Profile
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link" href="#" id="logoutBtn">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2" id="pageTitle">Dashboard Overview</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-custom" data-bs-toggle="modal" data-bs-target="#taskModal">
                            <i class="fas fa-plus me-2"></i>New Task
                        </button>
                    </div>
                </div>

                <!-- Alert container -->
                <div id="alert-container"></div>

                <!-- Overview Section -->
                <div id="overview-section" class="content-section">
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-tasks fa-2x mb-2"></i>
                                    <h3 id="totalTasks">0</h3>
                                    <p class="mb-0">Total Tasks</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                                    <h3 id="completedTasks">0</h3>
                                    <p class="mb-0">Completed</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-clock fa-2x mb-2"></i>
                                    <h3 id="pendingTasks">0</h3>
                                    <p class="mb-0">Pending</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-bell fa-2x mb-2"></i>
                                    <h3 id="upcomingReminders">0</h3>
                                    <p class="mb-0">Reminders</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Tasks and Upcoming Reminders -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Recent Tasks</h5>
                                </div>
                                <div class="card-body">
                                    <div id="recentTasks">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-spinner fa-spin"></i> Loading...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Upcoming Reminders</h5>
                                </div>
                                <div class="card-body">
                                    <div id="upcomingRemindersList">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-spinner fa-spin"></i> Loading...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tasks Section -->
                <div id="tasks-section" class="content-section" style="display: none;">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Search tasks..." id="taskSearch">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <select class="form-select" id="taskFilter">
                                <option value="">All Tasks</option>
                                <option value="pending">Pending</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-body">
                            <div id="tasksList">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> Loading tasks...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reminders Section -->
                <div id="reminders-section" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4>My Reminders</h4>
                        <button type="button" class="btn btn-custom" data-bs-toggle="modal" data-bs-target="#reminderModal">
                            <i class="fas fa-plus me-2"></i>New Reminder
                        </button>
                    </div>
                    
                    <div class="card">
                        <div class="card-body">
                            <div id="remindersList">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> Loading reminders...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Section -->
                <div id="profile-section" class="content-section" style="display: none;">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Profile Information</h5>
                                </div>
                                <div class="card-body">
                                    <form id="profileForm">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="profileUsername" class="form-label">Username</label>
                                                <input type="text" class="form-control" id="profileUsername" readonly>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="profileEmail" class="form-label">Email</label>
                                                <input type="email" class="form-control" id="profileEmail" readonly>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="profileFullName" class="form-label">Full Name</label>
                                            <input type="text" class="form-control" id="profileFullName" readonly>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="profileRole" class="form-label">Role</label>
                                                <input type="text" class="form-control" id="profileRole" readonly>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="profileStatus" class="form-label">Status</label>
                                                <input type="text" class="form-control" id="profileStatus" readonly>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Account Statistics</h5>
                                </div>
                                <div class="card-body">
                                    <div id="profileStats">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-spinner fa-spin"></i> Loading...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Task Modal -->
    <div class="modal fade" id="taskModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Task</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="taskForm">
                        <div class="mb-3">
                            <label for="taskTitle" class="form-label">Title</label>
                            <input type="text" class="form-control" id="taskTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="taskDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="taskDescription" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="taskPriority" class="form-label">Priority</label>
                                <select class="form-select" id="taskPriority">
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="taskStatus" class="form-label">Status</label>
                                <select class="form-select" id="taskStatus">
                                    <option value="pending" selected>Pending</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="taskDueDate" class="form-label">Due Date</label>
                            <input type="datetime-local" class="form-control" id="taskDueDate">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-custom" id="saveTaskBtn">Save Task</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reminder Modal -->
    <div class="modal fade" id="reminderModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Reminder</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="reminderForm">
                        <div class="mb-3">
                            <label for="reminderTitle" class="form-label">Title</label>
                            <input type="text" class="form-control" id="reminderTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="reminderDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="reminderDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="reminderTime" class="form-label">Reminder Time</label>
                            <input type="datetime-local" class="form-control" id="reminderTime" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="isRecurring">
                            <label class="form-check-label" for="isRecurring">
                                Recurring Reminder
                            </label>
                        </div>
                        <div class="mb-3" id="recurrencePatternDiv" style="display: none;">
                            <label for="recurrencePattern" class="form-label">Recurrence Pattern</label>
                            <select class="form-select" id="recurrencePattern">
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-custom" id="saveReminderBtn">Save Reminder</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/dashboard.js"></script>
</body>
</html>
