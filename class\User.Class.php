<?php
class User extends SQLQuery{
	public $table = 'user';
	// signup and signin file functions
	public function check_user_avalable($mobile){
		$query = "SELECT * FROM `user` WHERE `mobile_no` = '".$mobile."'";
		$data = parent::nonupdate_query($query);
		$number = parent::total_record($data);
		return $number;
	}
	public function add_user($post){
		$user_exists = $this->check_user_avalable($post['mobile_no']);

		if($user_exists=="0"){
			$query = parent::insert_record($this->table,$post);
			$data = parent::update_query($query);
		} else { 
			$_SESSION['message'] = 'Mobile number already registered.';
			header('location:'.basename($_SERVER['HTTP_REFERER'])); exit();
		}
	}
	public function login($mobile,$password){
		if(($mobile != NULL) AND ($mobile != '') AND ($password != NULL) AND ($password != '')){
			$query = "SELECT * FROM `" . $this->table . "` WHERE `mobile_no` = '" . $mobile . "'";
			$data = $this->nonupdate_query($query);	//echo $data . " user :". mysql_num_rows($data); exit;
			if(mysqli_num_rows($data)>=1){ 
				while($row = mysqli_fetch_array($data)){
					if($row['password'] == $password){   //md5($password))
						$_SESSION['user_id'] = $row['id'];	
						$_SESSION['user_type'] = $row['type'];
						//$_SESSION['message'] = 'Login Sucessfully...';
						header('location:testlist.php');
					}
					else{ $_SESSION['message'] = 'Password not match'; }
				}
			} else{ $_SESSION['message'] = 'Mobile No not match'; }
		}
		else{ $_SESSION['message'] = 'Enter value Properly'; }
	}
	public function logout(){
		unset($_SESSION['user_id']);
		unset($_SESSION['user_type']);
	}
	// used in user.php
	public function view_user_all(){
		$query = parent::select_table($this->table);		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function select_user_id($id){
		$query = parent::select_record($this->table,$id);		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function update_user($post){
		$query = parent::update_record($this->table,$post,$post['id']);
		$data = parent::update_query($query);
	}
	public function delete_user($id){
		$query = parent::delete_record($this->table,$id);
		$result = parent::update_query($query);
	}
	//
	public function islogin(){	
		if(isset($_SESSION['user_id'])){ return true;}
		else { return false; };	//else header('location:' . SITE_ROOT . 'login.php');
	}
	public function user_page(){	
		if($this->islogin()){ return true; } else{ header('location:signin.php'); }
	}
	public function foruser(){	
		if($this->islogin()){ return true; }
	}
	public function forauthor(){	
		if($this->foruser() and ($_SESSION['user_type']=='author' or $_SESSION['user_type']=='admin')){ return true; } 
	}
	public function foradmin(){	
		if($this->foruser() and $_SESSION['user_type']=='admin'){ return true; }
	}
	//used in userprofile.php to select supervisor
	public function view_author_all(){
		$query = "SELECT * FROM `".$this->table."` WHERE `type` = 'author'";		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function view_avalable_author($user_id){
		$query = "SELECT * FROM `user` WHERE  `user`.`type`= 'author' AND (`user`.`status`='public' OR  `user`.`id` IN (SELECT `user_supervisor`.`supervisor_id` FROM `user` JOIN `user_supervisor` 
		ON `user`.`id`=`user_supervisor`.`supervisor_id` AND `user_supervisor`.`user_id`=".$user_id."))";		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	//used in userprofile.php to select supervisor 
	public function select_supervisor($post){
		$post['user_id']=$_SESSION['user_id'];
		$query = parent::insert_record("user_supervisor",$post);
		$data = parent::update_query($query);
	}
	public function view_student_supervisor(){
		$query = "SELECT * FROM `user` JOIN `user_supervisor` ON `user`.`id`=`user_supervisor`.`supervisor_id` WHERE `user_supervisor`.`user_id`=".$_SESSION['user_id'];
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function delete_stusent_or_supervisor($id){
		$query = parent::delete_record("user_supervisor",$id);
		$result = parent::update_query($query);
	}
	//used in student.php to add student with supervisor and view her student
	public function add_supervisor($post){
		$query = parent::insert_record("user_supervisor",$post);
		$data = parent::update_query($query);
	}
	public function add_student($post){
		$query = parent::insert_record($this->table,$post);		
		$data = parent::update_query($query);
		echo $post_s['user_id'] = $this->last_inser_id;
		echo $post_s['supervisor_id'] =$_SESSION['user_id'];
		$this->add_supervisor($post_s);
	}
	public function view_author_student(){
		$query = "SELECT * FROM `user` JOIN `user_supervisor` ON `user`.`id`=`user_supervisor`.`user_id` WHERE `user_supervisor`.`supervisor_id`=".$_SESSION['user_id'];
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	//
	public function user_details(){	
		if(isset($_SESSION['user_id'])){
			$query = parent::select_record($this->table,$_SESSION['user_id']);
			$data = parent::nonupdate_query($query);
			$user_details = mysqli_fetch_array($data);
			return $user_details;
		}	
	}
	
}
?>