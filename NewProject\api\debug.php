<?php
/**
 * Debug Endpoint
 * Simple endpoint to test basic functionality
 */

// Start output buffering
ob_start();

// Set headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=UTF-8');

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    ob_end_clean();
    http_response_code(200);
    exit();
}

// Function to send JSON response
function sendJsonResponse($data, $status_code = 200) {
    ob_end_clean();
    http_response_code($status_code);
    echo json_encode($data, JSON_PRETTY_PRINT);
    exit();
}

$debug_info = [
    'status' => 'API is working',
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'php_version' => phpversion(),
    'current_directory' => __DIR__,
    'files_check' => []
];

// Check if required files exist
$required_files = [
    '../config/config.php',
    '../config/database.php',
    '../classes/User.php',
    '../classes/Database.php'
];

foreach ($required_files as $file) {
    $debug_info['files_check'][$file] = file_exists($file) ? 'EXISTS' : 'MISSING';
}

// Test config loading
try {
    if (file_exists('../config/config.php')) {
        require_once '../config/config.php';
        $debug_info['config_loaded'] = 'SUCCESS';
    } else {
        $debug_info['config_loaded'] = 'CONFIG FILE MISSING';
    }
} catch (Exception $e) {
    $debug_info['config_loaded'] = 'ERROR: ' . $e->getMessage();
}

// Test database connection
try {
    if (class_exists('Database')) {
        $database = new Database();
        $db = $database->getConnection();
        if ($db) {
            $debug_info['database_connection'] = 'SUCCESS';
            
            // Test if users table exists
            $stmt = $db->prepare("SHOW TABLES LIKE 'users'");
            $stmt->execute();
            $debug_info['users_table'] = $stmt->rowCount() > 0 ? 'EXISTS' : 'MISSING';
        } else {
            $debug_info['database_connection'] = 'FAILED';
        }
    } else {
        $debug_info['database_connection'] = 'DATABASE CLASS NOT FOUND';
    }
} catch (Exception $e) {
    $debug_info['database_connection'] = 'ERROR: ' . $e->getMessage();
}

// Test User class
try {
    if (class_exists('User')) {
        $debug_info['user_class'] = 'LOADED';
    } else {
        $debug_info['user_class'] = 'NOT FOUND';
    }
} catch (Exception $e) {
    $debug_info['user_class'] = 'ERROR: ' . $e->getMessage();
}

// Test POST data if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = file_get_contents('php://input');
    $debug_info['post_data'] = [
        'raw_input' => $input,
        'json_decode' => json_decode($input, true),
        'json_error' => json_last_error_msg()
    ];
}

sendJsonResponse($debug_info);
?>
