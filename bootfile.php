<?php
session_start();
ob_start();
//error_reporting(1);
ini_set('max_execution_time', 600); //300 seconds = 5 minutes

defined('DS') ? null : define('DS', "/");
defined('SITE_ROOT') ? null : define('SITE_ROOT', $_SERVER['DOCUMENT_ROOT'] . DS . "cris" . DS ."pretest.app" .DS);
defined('CLASS_PATH') ? null : define('CLASS_PATH', SITE_ROOT . DS . 'class' . DS);
defined('INC_PATH') ? null : define('INC_PATH', SITE_ROOT . DS . 'part' . DS);

$include_class = CLASS_PATH . 'Include.Class.php'; require_once((string)$include_class); unset($include_class);
if (isset($_GET['bootfile'])) echo "bootfile working"; 
?>