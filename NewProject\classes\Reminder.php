<?php
/**
 * Reminder Class
 * Handles CRUD operations for reminders
 */

class Reminder {
    private $conn;
    private $table_name = "reminders";
    
    public $id;
    public $user_id;
    public $task_id;
    public $title;
    public $description;
    public $reminder_time;
    public $is_recurring;
    public $recurrence_pattern;
    public $status;
    public $created_at;
    public $updated_at;
    
    public function __construct($db = null) {
        if ($db) {
            $this->conn = $db;
        } else {
            $database = new Database();
            $this->conn = $database->getConnection();
        }
    }
    
    // Create reminder
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET user_id=:user_id, task_id=:task_id, title=:title, 
                      description=:description, reminder_time=:reminder_time, 
                      is_recurring=:is_recurring, recurrence_pattern=:recurrence_pattern, 
                      status=:status";
        
        $stmt = $this->conn->prepare($query);
        
        // Sanitize
        $this->title = validateInput($this->title);
        $this->description = validateInput($this->description);
        
        // Bind values
        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":task_id", $this->task_id);
        $stmt->bindParam(":title", $this->title);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":reminder_time", $this->reminder_time);
        $stmt->bindParam(":is_recurring", $this->is_recurring);
        $stmt->bindParam(":recurrence_pattern", $this->recurrence_pattern);
        $stmt->bindParam(":status", $this->status);
        
        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            $this->logActivity('create', 'reminder', $this->id, [
                'title' => $this->title,
                'reminder_time' => $this->reminder_time
            ]);
            return true;
        }
        
        return false;
    }
    
    // Read user's reminders
    public function readByUser($user_id) {
        $query = "SELECT r.*, t.title as task_title 
                  FROM " . $this->table_name . " r
                  LEFT JOIN tasks t ON r.task_id = t.id
                  WHERE r.user_id = :user_id 
                  ORDER BY r.reminder_time ASC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Read all reminders (admin only)
    public function readAll() {
        $query = "SELECT r.*, u.username, u.full_name, t.title as task_title 
                  FROM " . $this->table_name . " r
                  LEFT JOIN users u ON r.user_id = u.id
                  LEFT JOIN tasks t ON r.task_id = t.id
                  ORDER BY r.reminder_time DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Read single reminder
    public function readOne() {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE id = ? LIMIT 0,1";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($row) {
            $this->user_id = $row['user_id'];
            $this->task_id = $row['task_id'];
            $this->title = $row['title'];
            $this->description = $row['description'];
            $this->reminder_time = $row['reminder_time'];
            $this->is_recurring = $row['is_recurring'];
            $this->recurrence_pattern = $row['recurrence_pattern'];
            $this->status = $row['status'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return true;
        }
        
        return false;
    }
    
    // Update reminder
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET task_id=:task_id, title=:title, description=:description, 
                      reminder_time=:reminder_time, is_recurring=:is_recurring, 
                      recurrence_pattern=:recurrence_pattern, status=:status 
                  WHERE id=:id";
        
        $stmt = $this->conn->prepare($query);
        
        // Sanitize
        $this->title = validateInput($this->title);
        $this->description = validateInput($this->description);
        
        // Bind values
        $stmt->bindParam(":task_id", $this->task_id);
        $stmt->bindParam(":title", $this->title);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":reminder_time", $this->reminder_time);
        $stmt->bindParam(":is_recurring", $this->is_recurring);
        $stmt->bindParam(":recurrence_pattern", $this->recurrence_pattern);
        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":id", $this->id);
        
        if ($stmt->execute()) {
            $this->logActivity('update', 'reminder', $this->id, [
                'title' => $this->title,
                'status' => $this->status
            ]);
            return true;
        }
        
        return false;
    }
    
    // Delete reminder
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        
        if ($stmt->execute()) {
            $this->logActivity('delete', 'reminder', $this->id, [
                'title' => $this->title
            ]);
            return true;
        }
        
        return false;
    }
    
    // Get upcoming reminders
    public function getUpcoming($user_id, $hours = 24) {
        $query = "SELECT r.*, t.title as task_title 
                  FROM " . $this->table_name . " r
                  LEFT JOIN tasks t ON r.task_id = t.id
                  WHERE r.user_id = :user_id 
                  AND r.reminder_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL :hours HOUR)
                  AND r.status = 'active'
                  ORDER BY r.reminder_time ASC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":hours", $hours);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Get overdue reminders
    public function getOverdue($user_id) {
        $query = "SELECT r.*, t.title as task_title 
                  FROM " . $this->table_name . " r
                  LEFT JOIN tasks t ON r.task_id = t.id
                  WHERE r.user_id = :user_id 
                  AND r.reminder_time < NOW()
                  AND r.status = 'active'
                  ORDER BY r.reminder_time DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Check if user owns this reminder
    public function isOwner($user_id) {
        if (!$this->readOne()) {
            return false;
        }
        return $this->user_id == $user_id;
    }
    
    // Log activity
    private function logActivity($action, $entity_type, $entity_id, $details = []) {
        $query = "INSERT INTO activity_logs 
                  SET user_id=:user_id, action=:action, entity_type=:entity_type, 
                      entity_id=:entity_id, details=:details, ip_address=:ip_address, 
                      user_agent=:user_agent";
        
        $stmt = $this->conn->prepare($query);
        
        $user_id = $_SESSION['user_id'] ?? null;
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $details_json = json_encode($details);
        
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":action", $action);
        $stmt->bindParam(":entity_type", $entity_type);
        $stmt->bindParam(":entity_id", $entity_id);
        $stmt->bindParam(":details", $details_json);
        $stmt->bindParam(":ip_address", $ip_address);
        $stmt->bindParam(":user_agent", $user_agent);
        
        $stmt->execute();
    }
}
?>
