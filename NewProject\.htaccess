RewriteEngine On

# Enable CORS for API requests
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

# Handle preflight OPTIONS requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# API routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/auth/(.*)$ api/auth.php [QSA,L]
RewriteRule ^api/tasks/(.*)$ api/tasks.php [QSA,L]
RewriteRule ^api/reminders/(.*)$ api/reminders.php [QSA,L]
RewriteRule ^api/admin/(.*)$ api/admin.php [QSA,L]
RewriteRule ^api/dashboard/(.*)$ api/dashboard.php [QSA,L]

# Fallback for other API requests
RewriteRule ^api/(.*)$ api/$1.php [QSA,L]
