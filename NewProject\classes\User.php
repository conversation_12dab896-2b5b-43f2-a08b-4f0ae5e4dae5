<?php
/**
 * User Class
 * Enhanced version based on PreTest.app User.Class.php
 */

class User {
    private $conn;
    private $table_name = "users";
    
    public $id;
    public $username;
    public $email;
    public $password;
    public $full_name;
    public $role;
    public $status;
    public $created_at;
    public $updated_at;
    
    public function __construct($db = null) {
        if ($db) {
            $this->conn = $db;
        } else {
            $database = new Database();
            $this->conn = $database->getConnection();
        }
    }
    
    // Create user
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET username=:username, email=:email, password=:password, 
                      full_name=:full_name, role=:role, status=:status";
        
        $stmt = $this->conn->prepare($query);
        
        // Sanitize
        $this->username = validateInput($this->username);
        $this->email = validateInput($this->email);
        $this->full_name = validateInput($this->full_name);
        $this->password = password_hash($this->password, PASSWORD_DEFAULT);
        
        // Bind values
        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":password", $this->password);
        $stmt->bindParam(":full_name", $this->full_name);
        $stmt->bindParam(":role", $this->role);
        $stmt->bindParam(":status", $this->status);
        
        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            $this->logActivity('create', 'user', $this->id, ['username' => $this->username]);
            return true;
        }
        
        return false;
    }
    
    // Read all users (admin only)
    public function readAll() {
        $query = "SELECT id, username, email, full_name, role, status, created_at, updated_at 
                  FROM " . $this->table_name . " 
                  ORDER BY created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Read single user
    public function readOne() {
        $query = "SELECT id, username, email, full_name, role, status, created_at, updated_at 
                  FROM " . $this->table_name . " 
                  WHERE id = ? LIMIT 0,1";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($row) {
            $this->username = $row['username'];
            $this->email = $row['email'];
            $this->full_name = $row['full_name'];
            $this->role = $row['role'];
            $this->status = $row['status'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return true;
        }
        
        return false;
    }
    
    // Update user
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET username=:username, email=:email, full_name=:full_name, 
                      role=:role, status=:status 
                  WHERE id=:id";
        
        $stmt = $this->conn->prepare($query);
        
        // Sanitize
        $this->username = validateInput($this->username);
        $this->email = validateInput($this->email);
        $this->full_name = validateInput($this->full_name);
        
        // Bind values
        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":full_name", $this->full_name);
        $stmt->bindParam(":role", $this->role);
        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":id", $this->id);
        
        if ($stmt->execute()) {
            $this->logActivity('update', 'user', $this->id, ['username' => $this->username]);
            return true;
        }
        
        return false;
    }
    
    // Delete user
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        
        if ($stmt->execute()) {
            $this->logActivity('delete', 'user', $this->id, ['username' => $this->username]);
            return true;
        }
        
        return false;
    }
    
    // Login function (similar to PreTest.app)
    public function login($username, $password) {
        $query = "SELECT id, username, email, password, full_name, role, status 
                  FROM " . $this->table_name . " 
                  WHERE (username = :username OR email = :username) AND status = 'active'";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":username", $username);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (password_verify($password, $row['password'])) {
                $_SESSION['user_id'] = $row['id'];
                $_SESSION['username'] = $row['username'];
                $_SESSION['user_role'] = $row['role'];
                $_SESSION['full_name'] = $row['full_name'];
                
                $this->logActivity('login', 'user', $row['id'], ['username' => $row['username']]);
                return true;
            }
        }
        
        return false;
    }
    
    // Logout function
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            $this->logActivity('logout', 'user', $_SESSION['user_id'], ['username' => $_SESSION['username']]);
        }
        
        session_unset();
        session_destroy();
        return true;
    }
    
    // Check if username/email exists
    public function userExists() {
        $query = "SELECT id FROM " . $this->table_name . " 
                  WHERE username = :username OR email = :email";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":email", $this->email);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }
    
    // Log user activity
    private function logActivity($action, $entity_type, $entity_id, $details = []) {
        $query = "INSERT INTO activity_logs 
                  SET user_id=:user_id, action=:action, entity_type=:entity_type, 
                      entity_id=:entity_id, details=:details, ip_address=:ip_address, 
                      user_agent=:user_agent";
        
        $stmt = $this->conn->prepare($query);
        
        $user_id = $_SESSION['user_id'] ?? $this->id ?? null;
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $details_json = json_encode($details);
        
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":action", $action);
        $stmt->bindParam(":entity_type", $entity_type);
        $stmt->bindParam(":entity_id", $entity_id);
        $stmt->bindParam(":details", $details_json);
        $stmt->bindParam(":ip_address", $ip_address);
        $stmt->bindParam(":user_agent", $user_agent);
        
        $stmt->execute();
    }
}
?>
