<?php
/**
 * Simple Login Endpoint
 * Direct login endpoint without routing
 */

// Start output buffering to catch any unexpected output
ob_start();

// Set headers first
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=UTF-8');

// Enable error reporting but don't display errors (we'll handle them)
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    ob_end_clean();
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_end_clean();
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// Function to send JSON response and exit
function sendJsonResponse($data, $status_code = 200) {
    ob_end_clean();
    http_response_code($status_code);
    echo json_encode($data);
    exit();
}

try {
    // Check if config file exists
    if (!file_exists('../config/config.php')) {
        sendJsonResponse(['error' => 'Configuration file not found'], 500);
    }

    // Include required files
    require_once '../config/config.php';
    
    // Get POST data
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        sendJsonResponse(['error' => 'Invalid JSON data: ' . json_last_error_msg()], 400);
    }

    // Validate input
    if (empty($data['username']) || empty($data['password'])) {
        sendJsonResponse(['error' => 'Username and password are required'], 400);
    }
    
    // Check if Database class exists
    if (!class_exists('Database')) {
        sendJsonResponse(['error' => 'Database class not found'], 500);
    }

    // Create database connection
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        sendJsonResponse(['error' => 'Database connection failed'], 500);
    }

    // Check if User class exists
    if (!class_exists('User')) {
        sendJsonResponse(['error' => 'User class not found'], 500);
    }

    // Create user object and attempt login
    $user = new User($db);

    if ($user->login($data['username'], $data['password'])) {
        sendJsonResponse([
            'success' => true,
            'message' => 'Login successful',
            'user' => [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'role' => $_SESSION['user_role'],
                'full_name' => $_SESSION['full_name']
            ]
        ], 200);
    } else {
        sendJsonResponse(['error' => 'Invalid credentials'], 401);
    }

} catch (Exception $e) {
    error_log('Login error: ' . $e->getMessage());
    sendJsonResponse(['error' => 'Internal server error: ' . $e->getMessage()], 500);
} catch (Error $e) {
    error_log('Login fatal error: ' . $e->getMessage());
    sendJsonResponse(['error' => 'Fatal error: ' . $e->getMessage()], 500);
}
?>
