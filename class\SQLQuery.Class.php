<?php
class SQLQuery{
	public $con;
	public $last_inser_id;
	public function connect(){
		//$this->con = mysqli_connect('localhost', 'root', '','pretest') or
		$this->con = mysqli_connect('localhost', 'u205105597_pretest', 'u205105597_PreTest@123','u205105597_pretest') or die('MySQL Connection Error : ' . mysqli_error($this->con));
		mysqli_set_charset($this->con,"utf8");
	}
	function disconnect(){
		mysqli_close($this->con);
	}
	public function update_query($query){
		if(1){
			$this->connect();
			if(mysqli_query($this->con,$query)){ 
				$this->last_inser_id = mysqli_insert_id($this->con);
				$this->disconnect();
				return 1; 
			} else{ return 0; }
		} else{ return "Problem this Update Query : " . $query; }
	}
	public function nonupdate_query($query){
		if(1){
			$this->connect();
			if($data = mysqli_query($this->con,$query)){ 
				$this->disconnect();
				return $data; 
			} else{ return "Problem With Query : " . mysqli_error($this->con); }
		} else{ return "Problem this Non-Update Query : " . $query; }
	}
	public function insert_record($table,$record){		
		foreach($record as $field => $value){
			$fields[] = "`" . $field . "`";
			$values[] = "'" . $value . "'";
		}
		$fields=implode(',',$fields);
		$values=implode(',',$values);

		return "INSERT INTO `" . $table . "`(" . $fields . ") VALUES(" . $values . ")";
	}
	public function update_record($table,$record,$id){
		$fields=array();
		foreach($record as $field => $value){
			$fields[] = "`" . $field . "` = '" . $value . "' ";
		}
		$new_fields = implode(',',$fields);
		return "UPDATE `" . $table . "` SET " . $new_fields . " WHERE `id`='" . $id . "'";		
	}
	public function delete_record($table,$id){
		return "DELETE FROM `" . $table . "` WHERE `id` = ' " . $id . "'";
	}
	public function select_table($table){
		return "SELECT * FROM `" . $table . "`";
	}
	public function select_record($table,$id){
		return "SELECT * FROM `" . $table . "` WHERE `id` = '" . $id . "'";
		
	}
	public function select_field($field,$table,$id){
		return "SELECT `" . $field . "` FROM `" . $table . "` WHERE `id` = '" . $id . "'";
	}
	public function create_array($data){
		$rows=array();
		if($this->total_record($data)){
			while($row=mysqli_fetch_array($data)){
				//if($this->total_record($data)==1){ return $row; }
				$rows[]=$row;
			} 
		}
		return $rows;
	}
	public function total_record($data){
		$total_record = mysqli_num_rows($data);
		if($total_record>0) {
			return $total_record;
		}			
		else return 0;
	}
}
?>