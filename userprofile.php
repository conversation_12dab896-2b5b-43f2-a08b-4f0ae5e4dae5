<?php include("bootfile.php"); 
	$user_ob->user_page();
		
	if(!empty($_POST)){		
		if(isset($_POST['supervisor_id'])){			
			$user_ob->select_supervisor($_POST);
			header('location:userprofile.php');
		}
		if(isset($_POST['id'])){
			if($_POST['password']==""){ unset($_POST['password']); }
			$user_ob->update_user($_POST);
			header('location:userprofile.php');
		}
	}
	if(isset($_REQUEST['supervisorid'])){
		$user_ob->delete_stusent_or_supervisor($_REQUEST['supervisorid']);
		header('location:userprofile.php');
	}
	if(!empty($_SESSION['user_id'])){
		$row = $user_ob->select_user_id($_SESSION['user_id']);
		$_POST=$row[0];
	}
?>
<?php include(INC_PATH."header.php"); ?>
<div class="container mt-2">
<h3>Account Details</h3>
<p>select your School or Institute.</p>
<form action="#" method="POST">
	<div class="row m-1">
		<div class="col"> 
	  	<?php $author_rows = $user_ob->view_avalable_author($_SESSION['user_id']); ?>
	  	<select class="form-select form-select-lg" name="supervisor_id">
		  	<?php foreach($author_rows as $author_row){ ?>
				<option value="<?=$author_row['id']?>"><?=$author_row['name']?></option>
			<?php } unset($author_rows); ?>	
		</select>
	  </div>
		<div class="col m-1">
			<button type="submit" class="btn btn-primary btn-block">Add</button>
		</div>
	</div>
	<div class="ms-4">
	<?php $supervisor_rows = $user_ob->view_student_supervisor(); ?>
	<?php foreach($supervisor_rows as $supervisor_row){ ?>	
		<span type="button" class="badge btn-dark"><?=$supervisor_row['name']?> <a href="?action=delete&supervisorid=<?=$supervisor_row['id']?>"><i class="fa fa-times" style="color:red"></i></a></span>
	<?php } unset($supervisor_row); ?>
	</div>
</form>
<p>select your Teachers.</p>
<form action="#" method="POST">
	<div class="row m-1">
		<div class="col"> 
	  	<?php $author_rows = $user_ob->view_avalable_author($_SESSION['user_id']); ?>
	  	<select class="form-select form-select-lg" name="supervisor_id">
		  	<?php foreach($author_rows as $author_row){ ?>
				<option value="<?=$author_row['id']?>"><?=$author_row['name']?></option>
			<?php } unset($author_rows); ?>	
		</select>
	  </div>
		<div class="col m-1">
			<button type="submit" class="btn btn-primary btn-block">Add</button>
		</div>
	</div>
	<div class="ms-4">
	<?php $supervisor_rows = $user_ob->view_student_supervisor(); ?>
	<?php foreach($supervisor_rows as $supervisor_row){ ?>	
		<span type="button" class="badge btn-dark"><?=$supervisor_row['name']?> <a href="?action=delete&supervisorid=<?=$supervisor_row['id']?>"><i class="fa fa-times" style="color:red"></i></a></span>
	<?php } unset($supervisor_row); ?>
	</div>
</form>
  <p class="mt-5">Edit details carefully. If anything wrong message in contact page for support.</p>
  <form action="#" method="POST">
  	<div class="row m-1">
		<div class="col">
			<label for="sel1">Change Student or Techer</label>
			<select class="form-select" name="type">
				<option <?php if(!empty($_POST['type']) and $_POST['type']=="user"){ echo 'selected="selected"'; } ?>  value="user">Student</option>
				<option <?php if(!empty($_POST['type']) and $_POST['type']=="author"){ echo 'selected="selected"'; } ?> value="author">Teacher</option>
			</select>
		</div>
		<div class="col">
	  		<label for="Status">Status</label>	  
			<select class="form-select" name="status">		  	
				<option <?php if(!empty($_POST['status']) and $_POST['status'] == "Public" ){ echo "selected"; } ?> >Public</option>
				<option <?php if(!empty($_POST['status']) and $_POST['status'] == "Private" ){ echo "selected"; } ?> >Private</option>
			</select>
	  	</div>	
	</div> 
	<div class="row m-1">
		<div class="col">
			<label for="surname">Surname</label>
			<input type="text" class="form-control" placeholder="Enter Surname" name="surname" value="<?php if(!empty($_POST['surname'])){ echo $_POST['surname']; } ?>" required="required" minlength="3" maxlength="20" />
		</div>
		<div class="col">
			<label for="name">Name</label>
			<input type="text" class="form-control" placeholder="Enter Your Name" name="name" value="<?php if(!empty($_POST['name'])){ echo $_POST['name']; } ?>" required="required" minlength="3" maxlength="20" />
		</div>
	</div>
    <div class="row m-1">
		<div class="col">
			<label for="mobile">Mobile No</label>
			<input type="number" class="form-control" placeholder="Enter Mobile no" name="mobile_no" value="<?php if(!empty($_POST['mobile_no'])){ echo $_POST['mobile_no']; } ?>" required="required" min="6276957193" size="10" />
		</div>	
		<div class="col">
			<label for="pwd">Password:</label>
			<input type="password" class="form-control" placeholder="Enter password" name="password" value="" minlength="4" maxlength="20" />
		</div>
    </div>
	<div class="row m-1">		
		<div class="col mt-4">
		<?php if(!empty($_SESSION['user_id'])){ ?>
			<input type="hidden" name="id" value="<?=$_POST['id']?>">
		<?php } ?>
		</div>
    </div>
	<div class="row m-4">
		<button type="submit" class="btn btn-primary btn-block">Submit</button>
	</div>
  </form>
</div>
<?php include(INC_PATH."footer.php"); ?>