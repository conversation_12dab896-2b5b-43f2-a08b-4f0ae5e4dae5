<?php
/**
 * Dashboard Data Endpoint
 * Simple dashboard data without complex includes
 */

// Start session
session_start();

// Set headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=UTF-8');

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit();
}

// Database configuration
$host = 'localhost';
$db_name = 'task_reminder_app';
$username = 'root';
$password = '';

try {
    // Create database connection
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    $user_id = $_SESSION['user_id'];
    
    // Get task statistics
    $task_stats_query = "
        SELECT 
            COUNT(*) as total_tasks,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_tasks,
            SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_tasks,
            SUM(CASE WHEN priority = 'high' AND status != 'completed' THEN 1 ELSE 0 END) as high_priority_pending
        FROM tasks 
        WHERE user_id = ?
    ";
    
    $stmt = $pdo->prepare($task_stats_query);
    $stmt->execute([$user_id]);
    $task_stats = $stmt->fetch();
    
    // Get recent tasks (limit 5)
    $recent_tasks_query = "
        SELECT id, title, description, priority, status, due_date, created_at
        FROM tasks 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 5
    ";
    
    $stmt = $pdo->prepare($recent_tasks_query);
    $stmt->execute([$user_id]);
    $recent_tasks = $stmt->fetchAll();
    
    // Get upcoming reminders (next 24 hours)
    $upcoming_reminders_query = "
        SELECT r.id, r.title, r.description, r.reminder_time, t.title as task_title
        FROM reminders r
        LEFT JOIN tasks t ON r.task_id = t.id
        WHERE r.user_id = ? 
        AND r.reminder_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 24 HOUR)
        AND r.status = 'active'
        ORDER BY r.reminder_time ASC
        LIMIT 5
    ";
    
    $stmt = $pdo->prepare($upcoming_reminders_query);
    $stmt->execute([$user_id]);
    $upcoming_reminders = $stmt->fetchAll();
    
    // Get overdue tasks
    $overdue_tasks_query = "
        SELECT id, title, priority, due_date
        FROM tasks 
        WHERE user_id = ? 
        AND due_date < NOW() 
        AND status != 'completed'
        ORDER BY due_date ASC
        LIMIT 5
    ";
    
    $stmt = $pdo->prepare($overdue_tasks_query);
    $stmt->execute([$user_id]);
    $overdue_tasks = $stmt->fetchAll();
    
    // Calculate completion rate
    $completion_rate = 0;
    if ($task_stats['total_tasks'] > 0) {
        $completion_rate = round(($task_stats['completed_tasks'] / $task_stats['total_tasks']) * 100, 2);
    }
    
    // Prepare response
    $dashboard_data = [
        'success' => true,
        'dashboard' => [
            'task_statistics' => $task_stats,
            'recent_tasks' => $recent_tasks,
            'upcoming_reminders' => $upcoming_reminders,
            'overdue_tasks' => $overdue_tasks,
            'summary' => [
                'upcoming_reminders_count' => count($upcoming_reminders),
                'overdue_tasks_count' => count($overdue_tasks),
                'completion_rate' => $completion_rate
            ]
        ]
    ];
    
    echo json_encode($dashboard_data);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Database error',
        'message' => $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error',
        'message' => $e->getMessage()
    ]);
}
?>
