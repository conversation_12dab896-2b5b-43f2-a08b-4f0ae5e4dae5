<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - TaskManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #dc3545 0%, #6f42c1 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.2);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .stat-card {
            background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
            color: white;
        }
        .btn-custom {
            background: linear-gradient(45deg, #dc3545, #6f42c1);
            border: none;
            color: white;
            border-radius: 25px;
        }
        .btn-custom:hover {
            color: white;
            transform: translateY(-1px);
        }
        .activity-item {
            border-left: 3px solid #dc3545;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <i class="fas fa-shield-alt fa-2x text-white mb-2"></i>
                        <h5 class="text-white">Admin Panel</h5>
                        <small class="text-white-50" id="adminWelcome">Welcome Admin!</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" data-section="overview">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="users">
                                <i class="fas fa-users me-2"></i>User Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="activities">
                                <i class="fas fa-chart-line me-2"></i>Activity Monitor
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="statistics">
                                <i class="fas fa-chart-bar me-2"></i>Statistics
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="tasks">
                                <i class="fas fa-tasks me-2"></i>All Tasks
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="reminders">
                                <i class="fas fa-bell me-2"></i>All Reminders
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link" href="#" id="logoutBtn">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2" id="pageTitle">Admin Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-custom" data-bs-toggle="modal" data-bs-target="#userModal">
                            <i class="fas fa-user-plus me-2"></i>Add User
                        </button>
                    </div>
                </div>

                <!-- Alert container -->
                <div id="alert-container"></div>

                <!-- Overview Section -->
                <div id="overview-section" class="content-section">
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <h3 id="totalUsers">0</h3>
                                    <p class="mb-0">Total Users</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-tasks fa-2x mb-2"></i>
                                    <h3 id="totalTasks">0</h3>
                                    <p class="mb-0">Total Tasks</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-bell fa-2x mb-2"></i>
                                    <h3 id="totalReminders">0</h3>
                                    <p class="mb-0">Total Reminders</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                                    <h3 id="totalActivities">0</h3>
                                    <p class="mb-0">Activities (30d)</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activities and System Overview -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Recent Activities</h5>
                                </div>
                                <div class="card-body">
                                    <div id="recentActivities">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-spinner fa-spin"></i> Loading...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">System Overview</h5>
                                </div>
                                <div class="card-body">
                                    <div id="systemOverview">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-spinner fa-spin"></i> Loading...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Section -->
                <div id="users-section" class="content-section" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h5 class="mb-0">User Management</h5>
                                </div>
                                <div class="col-auto">
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="Search users..." id="userSearch">
                                        <button class="btn btn-outline-secondary" type="button">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Username</th>
                                            <th>Full Name</th>
                                            <th>Email</th>
                                            <th>Role</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="usersTableBody">
                                        <tr>
                                            <td colspan="8" class="text-center text-muted">
                                                <i class="fas fa-spinner fa-spin"></i> Loading users...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activities Section -->
                <div id="activities-section" class="content-section" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h5 class="mb-0">Activity Monitor</h5>
                                </div>
                                <div class="col-auto">
                                    <div class="row g-2">
                                        <div class="col">
                                            <select class="form-select form-select-sm" id="activityFilter">
                                                <option value="">All Activities</option>
                                                <option value="task">Task Activities</option>
                                                <option value="reminder">Reminder Activities</option>
                                                <option value="user">User Activities</option>
                                            </select>
                                        </div>
                                        <div class="col">
                                            <input type="text" class="form-control form-control-sm" placeholder="Search..." id="activitySearch">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="activitiesList">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> Loading activities...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Section -->
                <div id="statistics-section" class="content-section" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Activity Statistics</h5>
                                </div>
                                <div class="card-body">
                                    <div id="activityStats">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-spinner fa-spin"></i> Loading...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Most Active Users</h5>
                                </div>
                                <div class="card-body">
                                    <div id="activeUsers">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-spinner fa-spin"></i> Loading...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tasks Section -->
                <div id="tasks-section" class="content-section" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">All Tasks</h5>
                        </div>
                        <div class="card-body">
                            <div id="allTasksList">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> Loading tasks...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reminders Section -->
                <div id="reminders-section" class="content-section" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">All Reminders</h5>
                        </div>
                        <div class="card-body">
                            <div id="allRemindersList">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> Loading reminders...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- User Modal -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <div class="mb-3">
                            <label for="userUsername" class="form-label">Username</label>
                            <input type="text" class="form-control" id="userUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="userEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="userEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="userFullName" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="userFullName" required>
                        </div>
                        <div class="mb-3">
                            <label for="userPassword" class="form-label">Password</label>
                            <input type="password" class="form-control" id="userPassword" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="userRole" class="form-label">Role</label>
                                <select class="form-select" id="userRole">
                                    <option value="user">User</option>
                                    <option value="admin">Admin</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="userStatus" class="form-label">Status</label>
                                <select class="form-select" id="userStatus">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-custom" id="saveUserBtn">Save User</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/admin-dashboard.js"></script>
</body>
</html>
