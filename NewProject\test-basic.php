<?php
/**
 * Basic PHP Test
 * Simple test to verify P<PERSON> is working
 */
?>
<!DOCTYPE html>
<html>
<head>
    <title>Basic PHP Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .box { border: 1px solid #ccc; padding: 20px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Basic PHP Test</h1>
    
    <div class="box">
        <h2>PHP Information</h2>
        <p class="success">✅ PHP is working!</p>
        <p><strong>PHP Version:</strong> <?php echo phpversion(); ?></p>
        <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
        <p><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        <p><strong>Current Directory:</strong> <?php echo __DIR__; ?></p>
    </div>
    
    <div class="box">
        <h2>File Structure Check</h2>
        <?php
        $files_to_check = [
            'config/config.php',
            'config/database.php',
            'classes/User.php',
            'api/simple-login.php',
            'database/schema.sql'
        ];
        
        foreach ($files_to_check as $file) {
            $exists = file_exists($file);
            $class = $exists ? 'success' : 'error';
            $icon = $exists ? '✅' : '❌';
            echo "<p class='$class'>$icon $file</p>";
        }
        ?>
    </div>
    
    <div class="box">
        <h2>Database Connection Test</h2>
        <?php
        try {
            $host = 'localhost';
            $username = 'root';
            $password = '';
            
            $pdo = new PDO("mysql:host=$host;charset=utf8", $username, $password);
            echo "<p class='success'>✅ MySQL connection successful</p>";
            
            // Check if database exists
            $db_name = 'task_reminder_app';
            $stmt = $pdo->query("SHOW DATABASES LIKE '$db_name'");
            if ($stmt->rowCount() > 0) {
                echo "<p class='success'>✅ Database '$db_name' exists</p>";
                
                // Connect to specific database and check tables
                $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8", $username, $password);
                $tables = ['users', 'tasks', 'reminders', 'activity_logs'];
                
                foreach ($tables as $table) {
                    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                    if ($stmt->rowCount() > 0) {
                        echo "<p class='success'>✅ Table '$table' exists</p>";
                    } else {
                        echo "<p class='error'>❌ Table '$table' missing</p>";
                    }
                }
            } else {
                echo "<p class='error'>❌ Database '$db_name' does not exist</p>";
                echo "<p class='info'>💡 You need to create the database and import the schema</p>";
            }
            
        } catch (PDOException $e) {
            echo "<p class='error'>❌ Database connection failed: " . $e->getMessage() . "</p>";
        }
        ?>
    </div>
    
    <div class="box">
        <h2>Next Steps</h2>
        <?php
        $db_exists = false;
        try {
            $pdo = new PDO("mysql:host=localhost;charset=utf8", 'root', '');
            $stmt = $pdo->query("SHOW DATABASES LIKE 'task_reminder_app'");
            $db_exists = $stmt->rowCount() > 0;
        } catch (Exception $e) {
            // Ignore
        }
        
        if (!$db_exists) {
            echo "<p class='info'>1. Run <a href='setup-database.php'>setup-database.php</a> to create the database</p>";
        } else {
            echo "<p class='success'>1. ✅ Database is ready</p>";
        }
        ?>
        <p class="info">2. Test API: <a href="api/simple-debug.php">simple-debug.php</a></p>
        <p class="info">3. Try login: <a href="views/login.html">login.html</a></p>
    </div>
    
    <div class="box">
        <h2>Error Logs</h2>
        <p class="info">If you're still getting errors, check your Apache error log:</p>
        <ul>
            <li>Windows XAMPP: <code>xampp/apache/logs/error.log</code></li>
            <li>Windows WAMP: <code>wamp/logs/apache_error.log</code></li>
            <li>Linux: <code>/var/log/apache2/error.log</code></li>
        </ul>
    </div>
</body>
</html>
