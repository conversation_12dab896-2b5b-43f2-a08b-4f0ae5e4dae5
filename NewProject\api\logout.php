<?php
/**
 * Logout Endpoint
 * Simple logout without complex includes
 */

// Start session
session_start();

// Set headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=UTF-8');

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Log the logout activity if user is logged in
    if (isset($_SESSION['user_id'])) {
        // Database configuration
        $host = 'localhost';
        $db_name = 'task_reminder_app';
        $username = 'root';
        $password = '';
        
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8", $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // Log logout activity
            $log_stmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address) 
                VALUES (?, 'logout', 'user', ?, ?, ?)
            ");
            $log_stmt->execute([
                $_SESSION['user_id'],
                $_SESSION['user_id'],
                json_encode(['username' => $_SESSION['username'] ?? '']),
                $_SERVER['REMOTE_ADDR'] ?? ''
            ]);
        } catch (PDOException $e) {
            // Ignore database errors during logout
        }
    }
    
    // Clear all session variables
    $_SESSION = array();
    
    // Destroy the session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // Destroy the session
    session_destroy();
    
    echo json_encode([
        'success' => true,
        'message' => 'Logged out successfully'
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Logout error',
        'message' => $e->getMessage()
    ]);
}
?>
