<!DOCTYPE html>
<html>
<head>
    <title>Simple Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
    </style>
</head>
<body>
    <h1>Simple API Test</h1>
    
    <div id="results"></div>
    
    <script>
        async function runTests() {
            const results = document.getElementById('results');
            
            // Test 1: Basic PHP
            try {
                const response1 = await fetch('api/test.php');
                const text1 = await response1.text();
                results.innerHTML += `<div class="test success">✅ Test 1 (Basic PHP): ${text1}</div>`;
            } catch (error) {
                results.innerHTML += `<div class="test error">❌ Test 1 Failed: ${error.message}</div>`;
            }
            
            // Test 2: Headers
            try {
                const response2 = await fetch('api/test2.php');
                const text2 = await response2.text();
                results.innerHTML += `<div class="test success">✅ Test 2 (Headers): <pre>${text2}</pre></div>`;
            } catch (error) {
                results.innerHTML += `<div class="test error">❌ Test 2 Failed: ${error.message}</div>`;
            }
            
            // Test 3: JSON
            try {
                const response3 = await fetch('api/test3.php');
                const json3 = await response3.json();
                results.innerHTML += `<div class="test success">✅ Test 3 (JSON): ${JSON.stringify(json3)}</div>`;
            } catch (error) {
                results.innerHTML += `<div class="test error">❌ Test 3 Failed: ${error.message}</div>`;
            }
            
            // Test 4: Database connection
            try {
                const response4 = await fetch('api/basic-login.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'test', password: 'test' })
                });
                const json4 = await response4.json();
                results.innerHTML += `<div class="test success">✅ Test 4 (Database): ${JSON.stringify(json4)}</div>`;
            } catch (error) {
                results.innerHTML += `<div class="test error">❌ Test 4 Failed: ${error.message}</div>`;
            }
        }
        
        // Run tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>
