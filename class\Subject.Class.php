<?php
class Subject extends SQLQuery{
	public $table = 'subject';
	public function add_subject($post){
		$query = parent::insert_record($this->table,$post);
		$data = parent::update_query($query);
	}
	public function update_subject($post){
		$query = parent::update_record($this->table,$post,$post['id']);
		$data = parent::update_query($query);
	}
	public function delete_subject($id){
		$query = parent::delete_record($this->table,$id);
		$result = parent::update_query($query);
	}
	public function view_subject_all(){
		$query = parent::select_table($this->table);		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function view_subject_add_by_user($user_id){
		$query = "SELECT * FROM `subject` WHERE `user_id` = ".$user_id;
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function select_subject_id($id){
		$query = parent::select_record($this->table,$id);		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}	
}
?>