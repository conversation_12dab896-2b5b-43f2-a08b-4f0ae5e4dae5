<?php
class Contact extends SQLQuery{
	public $table = 'contact';
	public function add_contact($post){
		$query = parent::insert_record($this->table,$post);
		$data = parent::update_query($query);
		$_SESSION["message"] = 'Feedback sent Sucessfully..';
	}
	public function delete_contact($id){
		$query = parent::delete_record($this->table,$id);
		$result = parent::update_query($query);
	}
	public function view_contact_all(){
		$query = parent::select_table($this->table);		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
}
?>