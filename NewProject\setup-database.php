<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup - TaskManager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>Database Setup</h3>
                    </div>
                    <div class="card-body">
                        <?php
                        // Database configuration
                        $host = 'localhost';
                        $db_name = 'task_reminder_app';
                        $username = 'root';
                        $password = '';

                        echo "<h4>Step 1: Testing Database Connection</h4>";
                        
                        try {
                            // Test connection without database first
                            $pdo = new PDO("mysql:host=$host;charset=utf8", $username, $password);
                            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                            echo "<div class='alert alert-success'>✅ MySQL connection successful</div>";
                            
                            // Check if database exists
                            $stmt = $pdo->query("SHOW DATABASES LIKE '$db_name'");
                            if ($stmt->rowCount() > 0) {
                                echo "<div class='alert alert-info'>✅ Database '$db_name' already exists</div>";
                            } else {
                                echo "<div class='alert alert-warning'>⚠️ Database '$db_name' does not exist</div>";
                                echo "<p>Creating database...</p>";
                                $pdo->exec("CREATE DATABASE $db_name");
                                echo "<div class='alert alert-success'>✅ Database '$db_name' created successfully</div>";
                            }
                            
                            // Connect to the specific database
                            $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8", $username, $password);
                            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                            
                            echo "<h4>Step 2: Checking Tables</h4>";
                            
                            // Check if tables exist
                            $required_tables = ['users', 'tasks', 'reminders', 'activity_logs'];
                            $existing_tables = [];
                            
                            $stmt = $pdo->query("SHOW TABLES");
                            while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
                                $existing_tables[] = $row[0];
                            }
                            
                            $missing_tables = array_diff($required_tables, $existing_tables);
                            
                            if (empty($missing_tables)) {
                                echo "<div class='alert alert-success'>✅ All required tables exist</div>";
                            } else {
                                echo "<div class='alert alert-warning'>⚠️ Missing tables: " . implode(', ', $missing_tables) . "</div>";
                                echo "<p>Creating tables from schema...</p>";
                                
                                // Read and execute schema
                                if (file_exists('database/schema.sql')) {
                                    $schema = file_get_contents('database/schema.sql');
                                    
                                    // Split by semicolon and execute each statement
                                    $statements = explode(';', $schema);
                                    foreach ($statements as $statement) {
                                        $statement = trim($statement);
                                        if (!empty($statement)) {
                                            try {
                                                $pdo->exec($statement);
                                            } catch (PDOException $e) {
                                                // Ignore errors for statements that might already exist
                                                if (strpos($e->getMessage(), 'already exists') === false) {
                                                    echo "<div class='alert alert-warning'>Warning: " . $e->getMessage() . "</div>";
                                                }
                                            }
                                        }
                                    }
                                    echo "<div class='alert alert-success'>✅ Schema executed successfully</div>";
                                } else {
                                    echo "<div class='alert alert-danger'>❌ Schema file not found at database/schema.sql</div>";
                                }
                            }
                            
                            echo "<h4>Step 3: Verifying Sample Data</h4>";
                            
                            // Check if admin user exists
                            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
                            $stmt->execute();
                            $admin_count = $stmt->fetchColumn();
                            
                            if ($admin_count > 0) {
                                echo "<div class='alert alert-success'>✅ Admin user exists</div>";
                            } else {
                                echo "<div class='alert alert-warning'>⚠️ Admin user not found, creating...</div>";
                                
                                $stmt = $pdo->prepare("
                                    INSERT INTO users (username, email, password, full_name, role) 
                                    VALUES ('admin', '<EMAIL>', ?, 'System Administrator', 'admin')
                                ");
                                $stmt->execute([password_hash('password', PASSWORD_DEFAULT)]);
                                echo "<div class='alert alert-success'>✅ Admin user created (username: admin, password: password)</div>";
                            }
                            
                            echo "<h4>Step 4: Final Test</h4>";
                            
                            // Test API endpoint
                            $test_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/debug.php';
                            echo "<p>Testing API endpoint: <a href='$test_url' target='_blank'>$test_url</a></p>";
                            
                            echo "<div class='alert alert-success'>";
                            echo "<h5>✅ Setup Complete!</h5>";
                            echo "<p>You can now:</p>";
                            echo "<ul>";
                            echo "<li><a href='views/login.html'>Login to the application</a></li>";
                            echo "<li><a href='api/debug.php'>Test the API</a></li>";
                            echo "<li>Use credentials: admin / password</li>";
                            echo "</ul>";
                            echo "</div>";
                            
                        } catch (PDOException $e) {
                            echo "<div class='alert alert-danger'>";
                            echo "<h5>❌ Database Error</h5>";
                            echo "<p>Error: " . $e->getMessage() . "</p>";
                            echo "<p>Please check:</p>";
                            echo "<ul>";
                            echo "<li>MySQL server is running</li>";
                            echo "<li>Database credentials are correct</li>";
                            echo "<li>User has permission to create databases</li>";
                            echo "</ul>";
                            echo "</div>";
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
