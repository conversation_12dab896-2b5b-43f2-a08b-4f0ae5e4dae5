<?php
class Standard extends SQLQuery{
	public $table = 'standard';
	// stander file functions
	public function add_standard($post){
		$query = parent::insert_record($this->table,$post);
		$data = parent::update_query($query);
	}	
	public function update_standard($post){
		$query = parent::update_record($this->table,$post,$post['id']);
		$data = parent::update_query($query);
	}
	public function delete_standard($id){
		$query = parent::delete_record($this->table,$id);
		$result = parent::update_query($query);
	}
	public function view_standard_all(){
		$query = parent::select_table($this->table);		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function view_avalable_standard($user_id){
		$query = "SELECT * FROM `standard` WHERE `status`='Public' AND `standard`.`id` NOT IN(SELECT DISTINCT `standard`.`id` FROM `standard` JOIN `user_topic` ON `standard`.`id`= `user_topic`.`standard_id` WHERE `user_topic`.`user_id`=".$user_id.") UNION SELECT DISTINCT `standard`.* FROM `standard` JOIN `user_supervisor` ON `user_supervisor`.`supervisor_id`=`standard`.`user_id` WHERE `standard`.`status`='Private' AND `standard`.`id` NOT IN(SELECT DISTINCT `standard`.`id` FROM `standard` JOIN `user_topic` ON `standard`.`id`= `user_topic`.`standard_id` WHERE `user_topic`.`user_id`=".$user_id.") AND `user_supervisor`.`user_id`=".$user_id;
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function view_standard_add_by_user($user_id){
		$query = "SELECT * FROM `standard` WHERE `user_id` =".$user_id;
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function select_standard_id($id){
		$query = parent::select_record($this->table,$id);		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	// used in testlist.php to find user prepatin topic.
	public function view_user_selected_standard($user_id){
		$query = "SELECT * FROM `standard` JOIN `user_topic` ON `user_topic`.`standard_id`=`standard`.`id` WHERE `user_topic`.`user_id`=".$user_id;		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function view_standard_subject($id){
		$query = "SELECT DISTINCT `subject`.`name` AS `subject_name`,`subject`.`id` AS `subject_id` FROM `paperset` JOIN `subject` ON `paperset`.`subject_id`=`subject`.`id` WHERE `paperset`.`standard_id`=".$id;		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	//testlist.php and userprofile.php for add and removed topic
	public function add_user_standard($post){
		$query = parent::insert_record('user_topic',$post);
		$data = parent::update_query($query);
	}
	public function delete_user_standard($id){
		$query = parent::delete_record('user_topic',$id);
		$result = parent::update_query($query);
	}	
}
?>