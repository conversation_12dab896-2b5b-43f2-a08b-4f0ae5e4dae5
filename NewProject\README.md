# Task & Reminder Management System

A comprehensive web application for managing tasks and reminders with user authentication, role-based access control, and admin monitoring capabilities. Built with modern PHP architecture and RESTful API design.

## 🚀 Features

### User Functionality
- **Authentication**: Secure user login/logout system
- **Dashboard**: Personalized dashboard with task statistics and upcoming reminders
- **Task Management**: Full CRUD operations for tasks with priorities and due dates
- **Reminder Management**: Create, update, and delete reminders with recurring options
- **Profile Management**: View and manage user profile information

### Admin Functionality
- **User Management**: Create, read, update, and delete users
- **Activity Monitoring**: Monitor all user activities across the system
- **Statistics Dashboard**: Comprehensive analytics and reporting
- **System Overview**: Real-time system health and usage metrics

## 🏗️ Architecture

### Technology Stack
- **Backend**: PHP 7.4+ with Object-Oriented Programming
- **Database**: MySQL 5.7+
- **Frontend**: Bootstrap 5, HTML5, CSS3, JavaScript (ES6+)
- **API**: RESTful API architecture
- **Authentication**: Session-based authentication

### Project Structure
```
NewProject/
├── api/                    # Simple API endpoints
│   ├── basic-login.php    # Login endpoint
│   ├── basic-register.php # Registration endpoint
│   ├── dashboard-data.php # Dashboard data
│   ├── tasks-crud.php     # Tasks CRUD operations
│   ├── reminders-crud.php # Reminders CRUD operations
│   └── logout.php         # Logout endpoint
├── assets/                # Static assets
│   └── js/                # JavaScript files
│       └── dashboard.js   # Dashboard functionality
├── classes/               # PHP classes (Models)
│   ├── User.php          # User management class
│   ├── Task.php          # Task management class
│   ├── Reminder.php      # Reminder management class
│   └── ActivityLog.php   # Activity logging class
├── config/               # Configuration files
│   ├── config.php        # Main configuration
│   └── database.php      # Database connection
├── database/             # Database files
│   └── schema.sql        # Database schema
├── views/                # Frontend views
│   ├── login.html        # Login page
│   ├── register.html     # Registration page
│   └── dashboard.html    # User dashboard
├── index.php             # Landing page
└── README.md             # This file
```

## 🛠️ Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- Modern web browser

### Setup Instructions

1. **Clone/Download the project**
   ```bash
   git clone <repository-url>
   cd NewProject
   ```

2. **Database Setup**
   - Create a MySQL database named `task_reminder_app`
   - Import the database schema:
   ```sql
   mysql -u username -p task_reminder_app < database/schema.sql
   ```

3. **Configuration**
   - Update database credentials in `config/database.php`:
   ```php
   private $host = 'localhost';
   private $db_name = 'task_reminder_app';
   private $username = 'your_username';
   private $password = 'your_password';
   ```

4. **Web Server Setup**
   - Place the project in your web server's document root
   - Ensure PHP has write permissions for session handling
   - Configure virtual host (optional)

5. **Access the Application**
   - Open your browser and navigate to the project URL
   - Default admin credentials: `admin` / `password`
   - Default user credentials: `john_doe` / `password`

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/basic-login.php` - User login
- `POST /api/basic-register.php` - User registration
- `POST /api/logout.php` - User logout

### Dashboard Endpoints
- `GET /api/dashboard-data.php` - Get dashboard overview data

### Task Endpoints
- `GET /api/tasks-crud.php` - Get user's tasks
- `POST /api/tasks-crud.php` - Create new task
- `PUT /api/tasks-crud.php` - Update task
- `DELETE /api/tasks-crud.php` - Delete task

### Reminder Endpoints
- `GET /api/reminders-crud.php` - Get user's reminders
- `POST /api/reminders-crud.php` - Create new reminder
- `PUT /api/reminders-crud.php` - Update reminder
- `DELETE /api/reminders-crud.php` - Delete reminder

## 🔒 Security Features

- **Input Validation**: All user inputs are validated and sanitized
- **SQL Injection Prevention**: PDO prepared statements used throughout
- **XSS Protection**: HTML special characters escaped
- **Authentication**: Session-based authentication system
- **Authorization**: Role-based access control (User/Admin)
- **Activity Logging**: All user actions are logged for audit purposes

## 🎨 User Interface

### Design Features
- **Responsive Design**: Mobile-first approach using Bootstrap 5
- **Modern UI**: Clean, professional interface with gradient themes
- **Interactive Elements**: Smooth animations and transitions
- **Accessibility**: ARIA labels and keyboard navigation support
- **Cross-browser Compatibility**: Works on all modern browsers

### User Experience
- **Intuitive Navigation**: Clear menu structure and breadcrumbs
- **Real-time Feedback**: Instant alerts and notifications
- **Progressive Enhancement**: Works without JavaScript (basic functionality)
- **Loading States**: Visual feedback during API calls

## 📊 Database Schema

### Core Tables
- **users**: User accounts and authentication
- **tasks**: Task management with priorities and due dates
- **reminders**: Reminder system with recurring options
- **activity_logs**: System activity monitoring

### Relationships
- Users have many tasks and reminders
- Reminders can be linked to tasks
- All activities are logged with user references

## 🚦 Usage Examples

### Creating a Task
```javascript
const taskData = {
    title: "Complete project documentation",
    description: "Write comprehensive README file",
    priority: "high",
    status: "pending",
    due_date: "2024-01-15 17:00:00"
};

fetch('/api/tasks', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(taskData)
});
```

### Setting a Reminder
```javascript
const reminderData = {
    title: "Team meeting",
    description: "Weekly standup meeting",
    reminder_time: "2024-01-12 14:00:00",
    is_recurring: true,
    recurrence_pattern: "weekly"
};

fetch('/api/reminders', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(reminderData)
});
```

## 🔧 Customization

### Adding New Features
1. Create new API endpoints in the `api/` directory
2. Add corresponding frontend JavaScript functions
3. Update the database schema if needed
4. Add new UI components to the views

### Styling Customization
- Modify CSS variables in the HTML files
- Update Bootstrap theme colors
- Add custom CSS classes for new components

## 🐛 Troubleshooting

### Common Issues
1. **Database Connection Error**: Check credentials in `config/database.php`
2. **Session Issues**: Ensure PHP session directory is writable
3. **API Errors**: Check browser console and PHP error logs
4. **Permission Denied**: Verify file permissions and user roles

### Debug Mode
Enable error reporting in `config/config.php`:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## 📝 License

This project is open source and available under the [MIT License](https://opensource.org/licenses/MIT).

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the code comments for implementation details

---

**Built with ❤️ using modern web technologies**
