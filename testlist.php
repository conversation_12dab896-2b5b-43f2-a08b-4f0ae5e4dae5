<?php
include("bootfile.php");
//$user_ob->user_page();
$standard_class = CLASS_PATH.'Standard.Class.php';	require_once((string)$standard_class);	unset($standard_class);
$standard_ob = new Standard();	
if(!empty($_POST) and isset($_POST['standard_id'])){
    $standard_ob->add_user_standard($_POST);
    header('location:testlist.php');  }
if(isset($_REQUEST['topicid'])){
  $standard_ob->delete_user_standard($_REQUEST['topicid']);
  header('location:testlist.php'); }    
$paper_class = CLASS_PATH . 'Paper.Class.php'; require_once((string)$paper_class); unset($paper_class);
$paper_ob = new Paper();
$question_class = CLASS_PATH . 'Question.Class.php'; require_once((string)$question_class); unset($question_class);
//$question_ob = new Question();
$paperset_class = CLASS_PATH . 'PaperSet.Class.php'; require_once((string)$paperset_class); unset($paperset_class);
$paperset_ob = new PaperSet(); 

include(INC_PATH . "header.php"); ?>
<div class="container mt-2">
<div class="row">
    <h2>Test List</h2> 
    <p>Add and removed more topic of exam</p>
    <?php $user_id = 2; if(!empty($_SESSION['user_id'])){ $user_id = $_SESSION['user_id']; }  ?>
    <form action="#" method="POST">
      <div class="row">
        <div class="col-8">
          <?php $standard_rows = $standard_ob->view_avalable_standard($user_id); ?>
          <select class=" form-select-sm selectpicker d-grid col-12" name="standard_id" data-show-subtext="true" data-live-search="true">
            <optgroup label="Select or Search">  
              <?php foreach($standard_rows as $standard_row){ ?>
              <?php $user_row = $user_ob->select_user_id($standard_row['user_id'])?>
              <option value="<?=$standard_row['id']?>"><?=$standard_row['name']?>: <?=$user_row[0][2]?></option>
              <?php } unset($standard_row); ?>
            </<optgroup>
          </select>
        </div>
        <input type="hidden" name="user_id" value="<?=$user_id?>">
        <div class="col-4 mt-1 d-grid gap-4">
          <button type="submit" class="btn btn-sm btn-primary">Add</button>
        </div>
      </div>
      </form>
    <p>Paper Listed for the preparation of    
      <?php $standard_rows1 = $standard_ob->view_user_selected_standard($user_id); ?>
      <?php foreach($standard_rows1 as $standard_row1){ ?>	
        <span type="button" class="badge btn-dark"><?=$standard_row1['name']?> 
          <a href="?action=delete&topicid=<?=$standard_row1['id']?>">
          <i class="fa fa-times" style="color:red"></i></a></span>
      <?php } ?></p>
</div>
<?php foreach($standard_rows1 as $standard_row1){ ?>
  <div class="mt-5">
    <h5> Subjects for <?=$standard_row1['name']?></h5>
    <p>Click on <strong> Subject </strong> for test.</p>
    
    <div id="accordion">
      <?php $subject_rows = $standard_ob->view_standard_subject($standard_row1['standard_id']); ?>
      <?php foreach($subject_rows as $subject_row){ ?>	
      <div class="card">
        <div class="card-header" style="padding: 0;">
          <button class="accordion-button collapsed btn" style="padding: 0.5rem;" data-bs-toggle="collapse" href="#<?php echo $subject_row['subject_name'].$standard_row1['standard_id']; ?>">
            <?=$subject_row['subject_name']?>
      </button>
        </div>
        <div id="<?php echo $subject_row['subject_name'].$standard_row1['standard_id']; ?>" class="collapse" data-bs-parent="#accordion">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped table-bordered">
                <thead>
                  <tr>
                    <th>No</th>
                    <th>Paper Name</th>
                    <th>Total Questions</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody style="height: 100px;overflow-y: auto;">
                  <?php $rows =  $paper_ob->view_user_standard_subject_paper($user_id,$standard_row1['standard_id'],$subject_row['subject_id']); $k = 0; ?>
                  <?php foreach ($rows as $row) {
                    $k++; ?>
                    <tr>
                      <td><?= $k ?></td>
                      <td><?= $row["name"] ?></td>
                      <td><?= $paperset_ob->select_paperset_no_of_question($row['id']) ?></td>
                      <td><a href="exam.php?paperid=<?= $row['id'] ?>"><i class="fa fa-play" title="play test" style="color:green"></i></a></td>
                    </tr>
                  <?php } ?>
                </tbody>
              </table>
            </div>
        </div>
        </div>
      </div>
      <?php } ?>
    </div>
  </div>
  <?php } unset($standard_row1); ?></p>
</div>
<?php include(INC_PATH . "footer.php"); ?>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.0/js/bootstrap.bundle.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.18/dist/css/bootstrap-select.min.css">
<script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.18/dist/js/bootstrap-select.min.js"></script>