<?php include("bootfile.php"); 
	$user_ob->user_page();
	$standard_class = CLASS_PATH.'Standard.Class.php';	require_once((string)$standard_class);	unset($standard_class);
	$standard_ob = new Standard();
		
	if(!empty($_POST) and isset($_POST['id'])){
		$standard_ob->update_standard($_POST);
		header('location:standard.php');
	}
	else if(!empty($_POST)){
		$standard_ob->add_standard($_POST);
		header('location:standard.php');
	}
	else{ }
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="delete"){
		$standard_ob->delete_standard($_REQUEST['id']);
		header('location:standard.php');
	}
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){
		$row = $standard_ob->select_standard_id($_REQUEST['id']);
		$_POST=$row[0];
	}
?>
<?php include(INC_PATH."header.php"); ?>
<div class="container mt-2">
  <h3>Exam(Standard)</h3>
  <p>Standerd name are listed in Preparation topic</p>
  <form action="#" method="POST">
	<div class="row m-1"> 
		<div class="col">
			<label for="surname">Exam(Standard) Name:</label>
			<input type="text" class="form-control" placeholder="Enter Standard Name" name="name" value="<?php if(!empty($_POST['name'])){ echo $_POST['name']; } ?>" >
		</div>
		<div class="col">
	  	<label for="Status">Status:</label>	  
	  	<select class="form-select" name="status">		  	
			<option <?php if(!empty($_REQUEST['status']) and $_REQUEST['status'] == $standard_row['status']){ echo "selected"; } ?> >Public</option>
			<option <?php if(!empty($_REQUEST['status']) and $_REQUEST['status'] == $standard_row['status']){ echo "selected"; } ?> >Private</option>
			<option <?php if(!empty($_REQUEST['status']) and $_REQUEST['status'] == $standard_row['status']){ echo "selected"; } ?> >Draft</option>
		</select>
	  </div>
		<?php if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){ ?>
			<input type="hidden" name="id" value="<?=$_POST['id']?>">
		<?php } ?>
		<input type="hidden" name="user_id" value="<?=$_SESSION['user_id']?>">
		<div class="row p-4">
			<button type="submit" class="btn btn-primary btn-block">Submit</button>
		</div>
	</div>
  </form>

  <h2>View all Exam(Standard) name</h2>
  <p>Exam(Standerd) is just preparation topic name.</p>
  <div class="table-responsive">
  <table class="table table-striped">
    <thead>
      <tr>
        <th>No</th>
        <th>Exam(Standerd) Name</th> 
		<th>Status</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
	<?php $rows = $standard_ob->view_standard_add_by_user($_SESSION['user_id']); $k=0; ?>
	<?php foreach($rows as $row){ $k++; ?>
      <tr>
        <td><?=$k?></td>
        <td><?=$row["name"]?></td>
		<td><?=$row["status"]?></td>
        <td><a href="?action=edit&id=<?=$row['id']?>"><i class="fa fa-edit"></i></a>
			<a href="?action=delete&id=<?=$row['id']?>"><i class="fa fa-times" style="color:red"></i></a></td>
      </tr>
	<?php } ?>
    </tbody>
  </table>
	</div>
</div>
<?php include(INC_PATH."footer.php"); ?>