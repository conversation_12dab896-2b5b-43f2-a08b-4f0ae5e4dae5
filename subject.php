<?php include("bootfile.php"); 
	$user_ob->user_page();
	$subject_class = CLASS_PATH.'Subject.Class.php';	require_once((string)$subject_class);	unset($subject_class);
	$subject_ob = new Subject();
		
	if(!empty($_POST) and isset($_POST['id'])){
		$subject_ob->update_subject($_POST);
		header('location:subject.php');
	}
	else if(!empty($_POST)){
		$subject_ob->add_subject($_POST);
		header('location:subject.php');
	}
	else{ }
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="delete"){
		$subject_ob->delete_subject($_REQUEST['id']);
		header('location:subject.php');
	}
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){
		$row = $subject_ob->select_subject_id($_REQUEST['id']);
		$_POST=$row[0];
	}
?>
<?php include(INC_PATH."header.php"); ?>
<div class="container mt-2">
  <h3>Subject</h3>
  <p>Subject are commonly used for every standard</p>
  <form action="#" method="POST">
	<div class="row m-1"> 
		<div class="row">
			<label for="surname">Subject Name :</label>
			<input type="text" class="form-control" placeholder="Enter Subject Name" name="name" value="<?php if(!empty($_POST['name'])){ echo $_POST['name']; } ?>" >
		</div>
		<input type="hidden" name="user_id" value="<?=$_SESSION['user_id']?>">
		<?php if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){ ?>
			<input type="hidden" name="id" value="<?=$_POST['id']?>">
		<?php } ?>	
		<div class="row p-4">
			<button type="submit" class="btn btn-primary btn-block">Submit</button>
		</div>
	</div>
  </form>

  <h2>View all subjects name</h2>
  <p>Subject used in one then more time.</p>  
  <div class="table-responsive">
  <table class="table table-striped">
    <thead>
      <tr>
        <th>No</th>
        <th>Subject Name</th> 
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
	<?php $rows = $subject_ob->view_subject_add_by_user($_SESSION['user_id']); $k=0; ?>
	<?php foreach($rows as $row){ $k++; ?>
      <tr>
        <td><?=$k?></td>
        <td><?=$row["name"]?></td>
        <td><a href="?action=edit&id=<?=$row['id']?>"><i class="fa fa-edit"></i></a>
			<a href="?action=delete&id=<?=$row['id']?>"><i class="fa fa-times" style="color:red"></i></a></td>
      </tr>
	<?php } ?>
    </tbody>
  </table>
	</div>
</div>
<?php include(INC_PATH."footer.php"); ?>