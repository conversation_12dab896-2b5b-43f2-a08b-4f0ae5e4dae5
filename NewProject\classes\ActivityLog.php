<?php
/**
 * ActivityLog Class
 * Handles activity monitoring for admin
 */

class ActivityLog {
    private $conn;
    private $table_name = "activity_logs";
    
    public $id;
    public $user_id;
    public $action;
    public $entity_type;
    public $entity_id;
    public $details;
    public $ip_address;
    public $user_agent;
    public $created_at;
    
    public function __construct($db = null) {
        if ($db) {
            $this->conn = $db;
        } else {
            $database = new Database();
            $this->conn = $database->getConnection();
        }
    }
    
    // Read all activity logs (admin only)
    public function readAll($limit = 100, $offset = 0) {
        $query = "SELECT a.*, u.username, u.full_name 
                  FROM " . $this->table_name . " a
                  LEFT JOIN users u ON a.user_id = u.id
                  ORDER BY a.created_at DESC
                  LIMIT :limit OFFSET :offset";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Read activity logs by user
    public function readByUser($user_id, $limit = 50, $offset = 0) {
        $query = "SELECT a.*, u.username, u.full_name 
                  FROM " . $this->table_name . " a
                  LEFT JOIN users u ON a.user_id = u.id
                  WHERE a.user_id = :user_id
                  ORDER BY a.created_at DESC
                  LIMIT :limit OFFSET :offset";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Read activity logs by entity type
    public function readByEntityType($entity_type, $limit = 50, $offset = 0) {
        $query = "SELECT a.*, u.username, u.full_name 
                  FROM " . $this->table_name . " a
                  LEFT JOIN users u ON a.user_id = u.id
                  WHERE a.entity_type = :entity_type
                  ORDER BY a.created_at DESC
                  LIMIT :limit OFFSET :offset";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":entity_type", $entity_type);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Get activity statistics
    public function getStatistics($days = 30) {
        $query = "SELECT 
                    COUNT(*) as total_activities,
                    COUNT(DISTINCT user_id) as active_users,
                    SUM(CASE WHEN action = 'login' THEN 1 ELSE 0 END) as total_logins,
                    SUM(CASE WHEN entity_type = 'task' THEN 1 ELSE 0 END) as task_activities,
                    SUM(CASE WHEN entity_type = 'reminder' THEN 1 ELSE 0 END) as reminder_activities,
                    SUM(CASE WHEN entity_type = 'user' THEN 1 ELSE 0 END) as user_activities
                  FROM " . $this->table_name . " 
                  WHERE created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":days", $days);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    // Get daily activity chart data
    public function getDailyActivityChart($days = 7) {
        $query = "SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as activity_count,
                    COUNT(DISTINCT user_id) as unique_users
                  FROM " . $this->table_name . " 
                  WHERE created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                  GROUP BY DATE(created_at)
                  ORDER BY date ASC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":days", $days);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Get most active users
    public function getMostActiveUsers($limit = 10, $days = 30) {
        $query = "SELECT 
                    u.id, u.username, u.full_name,
                    COUNT(a.id) as activity_count,
                    MAX(a.created_at) as last_activity
                  FROM users u
                  LEFT JOIN " . $this->table_name . " a ON u.id = a.user_id
                  WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                  GROUP BY u.id, u.username, u.full_name
                  ORDER BY activity_count DESC
                  LIMIT :limit";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":days", $days);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Get recent activities for dashboard
    public function getRecentActivities($limit = 20) {
        $query = "SELECT a.*, u.username, u.full_name 
                  FROM " . $this->table_name . " a
                  LEFT JOIN users u ON a.user_id = u.id
                  ORDER BY a.created_at DESC
                  LIMIT :limit";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Search activities
    public function search($search_term, $limit = 50, $offset = 0) {
        $search_term = "%{$search_term}%";
        
        $query = "SELECT a.*, u.username, u.full_name 
                  FROM " . $this->table_name . " a
                  LEFT JOIN users u ON a.user_id = u.id
                  WHERE u.username LIKE :search_term 
                     OR u.full_name LIKE :search_term
                     OR a.action LIKE :search_term
                     OR a.entity_type LIKE :search_term
                  ORDER BY a.created_at DESC
                  LIMIT :limit OFFSET :offset";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":search_term", $search_term);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Clean old logs (for maintenance)
    public function cleanOldLogs($days = 90) {
        $query = "DELETE FROM " . $this->table_name . " 
                  WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":days", $days);
        
        return $stmt->execute();
    }
}
?>
