<?php include("bootfile.php"); 
	$user_ob->user_page();
	$paper_class = CLASS_PATH.'Paper.Class.php';	require_once((string)$paper_class);	unset($paper_class);
	$paper_ob = new Paper(); 
	
	if(!empty($_POST) and isset($_POST['id'])){
		$user_ob->update_user($_POST);
		header('location:student.php');
	}
	else if(!empty($_POST)){
		$user_ob->add_student($_POST);
		header('location:student.php');
	}
	else{ }
	if(isset($_REQUEST['studentid'])){
		$user_ob->delete_stusent_or_supervisor($_REQUEST['studentid']);
		header('location:student.php');
	}
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){
		$row = $user_ob->select_user_id($_REQUEST['id']);
		$_POST=$row[0];
	} ?>
<?php include(INC_PATH."header.php"); ?>
<div class="container mt-2">
<h3>Add Techer</h3>
  <form action="#" method="POST">
	<div class="row m-1">
		<div class="col">
			<label for="surname">Surname</label>
			<input type="text" class="form-control" placeholder="Enter Surname" name="surname" value="<?php if(!empty($_POST['surname'])){ echo $_POST['surname']; } ?>" required="required" minlength="3" maxlength="20" />
		</div>
		<div class="col">
			<label for="name">Name</label>
			<input type="text" class="form-control" placeholder="Enter Your Name" name="name" value="<?php if(!empty($_POST['name'])){ echo $_POST['name']; } ?>" required="required" minlength="3" maxlength="20" />
		</div>
	</div>
    <div class="row m-1">
		<div class="col">
			<label for="mobile">Mobile No</label>
			<input type="number" class="form-control" placeholder="Enter Mobile no" name="mobile_no" value="<?php if(!empty($_POST['mobile_no'])){ echo $_POST['mobile_no']; } ?>" required="required" min="6276957193" max="9999999999" />
		</div>	
		<div class="col">
			<label for="pwd">Password:</label>
			<input type="password" class="form-control" placeholder="Enter password" name="password"  minlength="4" maxlength="20" />
		</div>
    </div>
	<input type="hidden" name="type" value="author">
	<?php if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){ ?>
		<input type="hidden" name="id" value="<?=$_POST['id']?>">
	<?php } ?>
	<div class="row m-4">
		<button type="submit" class="btn btn-primary btn-block">Submit</button>
	</div>
  </form>

  <h2>View All Teachers</h2>
  <p>List of  All Teachers :</p>
  <?php $rows = $user_ob->view_author_student(); $k=0; ?>
  <div class="table-responsive">
  <table class="table table-striped">
    <thead>
      <tr>
        <th>No</th>
        <th>Surname</th> 
        <th>Name</th>
		<th>Mobile No</th>
      </tr>
    </thead>
    <tbody>
	<?php foreach($rows as $row){ $k++; ?>
      <tr>
        <td><?=$k?></td>
		<td><?=$row["surname"]?></td>
        <td><?=$row["name"]?></td>
		<td><?=$row["mobile_no"]?></td>
        <td><a href="?action=edit&id=<?=$row['id']?>"><i class="fa fa-edit"></i></a>
		<a href="?action=delete&studentid=<?=$row['id']?>"><i class="fa fa-times" style="color:red"></i></a></td>
      </tr>
	<?php } ?>
    </tbody>
  </table>
  </div>
</div>
<?php include(INC_PATH."footer.php"); ?>