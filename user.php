<?php include("bootfile.php"); 
	$user_ob->user_page();
	if($_SESSION['user_type']!="Boss"){ header('location:index.php'); }
	$paper_class = CLASS_PATH.'Paper.Class.php';	require_once((string)$paper_class);	unset($paper_class);
	$paper_ob = new Paper();
	
	if(!empty($_POST) and isset($_POST['id'])){
		$user_ob->update_user($_POST);
		header('location:user.php');
	}
	else if(!empty($_POST)){
		$user_ob->add_user($_POST);
		header('location:user.php');
	}
	else{ }
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="delete"){
		$user_ob->delete_user($_REQUEST['id']);
		header('location:user.php');
	}
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){
		$row = $user_ob->select_user_id($_REQUEST['id']);
		$_POST=$row[0];
	} ?>
<?php include(INC_PATH."header.php"); ?>
<div class="container mt-2">
<h3>Add User</h3>
  <form action="#" method="POST">
	<div class="row m-1">
		<div class="col">
			<label for="surname">Surname</label>
			<input type="text" class="form-control" placeholder="Enter Surname" name="surname" value="<?php if(!empty($_POST['surname'])){ echo $_POST['surname']; } ?>" />
		</div>
		<div class="col">
			<label for="name">Name</label>
			<input type="text" class="form-control" placeholder="Enter Your Name" name="name" value="<?php if(!empty($_POST['name'])){ echo $_POST['name']; } ?>" />
		</div>
	</div>
    <div class="row m-1">
		<div class="col">
			<label for="mobile">Mobile No</label>
			<input type="number" class="form-control" placeholder="Enter Mobile no" name="mobile_no" value="<?php if(!empty($_POST['mobile_no'])){ echo $_POST['mobile_no']; } ?>" />
		</div>	
		<div class="col">
			<label for="pwd">Password:</label>
			<input type="password" class="form-control" placeholder="Enter password" name="password" value="<?php if(!empty($_POST['password'])){ echo $_POST['password']; } ?>" />
		</div>
    </div>
	<div class="row m-1">
		<div class="col">
			<label for="mobile">User Type</label>
			<input type="text" class="form-control" placeholder="Enter User Type" name="type" value="<?php if(!empty($_POST['type'])){ echo $_POST['type']; } ?>" />
		</div>	
		<div class="col mt-4">
		<?php if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){ ?>
			<input type="hidden" name="id" value="<?=$_POST['id']?>">
		<?php } ?>
		</div>
    </div>
	<div class="row m-4">
		<button type="submit" class="btn btn-primary btn-block">Submit</button>
	</div>
  </form>

  <h2>View All User</h2>
  <p>List of  All user :</p>
  <?php $rows = $user_ob->view_user_all(); $k=0; ?>
  <div class="table-responsive">
  <table class="table table-striped">
    <thead>
      <tr>
        <th>No</th>
        <th>Surname</th> 
        <th>Name</th>
		<th>Mobile No</th>
		<th>Password</th>
		<th>Type</th>
      </tr>
    </thead>
    <tbody>
	<?php foreach($rows as $row){ $k++; ?>
      <tr>
        <td><?=$k?></td>
		<td><?=$row["surname"]?></td>
        <td><?=$row["name"]?></td>
		<td><?=$row["mobile_no"]?></td>
		<td><?=$row["password"]?></td>
		<td><?=$row["type"]?></td>
        <td>
			<a href="?action=edit&id=<?=$row['id']?>"><i class="fa fa-edit"></i></a>
			<a href="?action=delete&id=<?=$row['id']?>"><i class="fa fa-times" style="color:red"></i></a>
		</td>
      </tr>
	<?php } ?>
    </tbody>
  </table>
  </div>
</div>
<?php include(INC_PATH."footer.php"); ?>