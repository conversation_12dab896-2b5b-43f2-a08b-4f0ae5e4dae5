<?php include("bootfile.php"); 
	$user_ob->user_page();
	$papersection_class = CLASS_PATH.'PaperSection.Class.php';	require_once((string)$papersection_class);	unset($papersection_class);
	$papersection_ob = new PaperSection();
	
	if(!empty($_POST) and isset($_POST['id'])){
		$papersection_ob->update_papersection($_POST);
		header('location:papersection.php');
	}
	else if(!empty($_POST)){
		$papersection_ob->add_papersection($_POST);
		header('location:papersection.php');
	}
	else{ }
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="delete"){
		$papersection_ob->delete_papersection($_REQUEST['id']);
		header('location:papersection.php');
	}
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){
		$row = $papersection_ob->select_papersection_id($_REQUEST['id']);
		$_POST=$row[0];
	} 
?>
<?php include(INC_PATH."header.php"); ?>
<div class="container mt-2">
  <h3>Paper Section</h3>
  <p>Paper section used in one then more time.</p>
  <form action="#" method="POST">
	<div class="row m-1"> 
		<div class="row">
			<label for="surname">Section Name</label>
			<input type="text" class="form-control" placeholder="Enter Section Name" name="name" value="<?php if(!empty($_POST['name'])){ echo $_POST['name']; } ?>" >
		</div>
		<?php if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){ ?>
			<input type="hidden" name="id" value="<?=$_POST['id']?>">
		<?php } ?>	
		<input type="hidden" name="user_id" value="<?=$_SESSION['user_id']?>">
		<div class="row p-4">
			<button type="submit" class="btn btn-primary btn-block">Submit</button>
		</div>
	</div>
  </form>

  <h2>All Paper Sections</h2>
  <p>Don't add duplicate section. Delete and Edit are affect in old papers.</p>
  <?php $rows = $papersection_ob->view_papersection_add_by_user($_SESSION['user_id']); $k=0; ?>  
  <div class="table-responsive">
  <table class="table table-striped">
    <thead>
      <tr>
        <th>No</th>
        <th>Section Name</th> 
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
	<?php foreach($rows as $row){ $k++; ?>
      <tr>
        <td><?=$k?></td>
        <td><?=$row["name"]?></td>
        <td><a href="?action=edit&id=<?=$row['id']?>"><i class="fa fa-edit"></i></a> 
			<a href="?action=delete&id=<?=$row['id']?>"><i class="fa fa-times" style="color:red"></i></a></td>
      </tr>
	<?php } ?>
    </tbody>
  </table>
  </div>
</div>
<?php include(INC_PATH."footer.php"); ?>