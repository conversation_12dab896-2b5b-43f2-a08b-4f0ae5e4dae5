<?php
/**
 * Reminders CRUD Endpoint
 * Simple reminders management without complex includes
 */

// Start session
session_start();

// Set headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=UTF-8');

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit();
}

// Database configuration
$host = 'localhost';
$db_name = 'task_reminder_app';
$username = 'root';
$password = '';

$method = $_SERVER['REQUEST_METHOD'];
$user_id = $_SESSION['user_id'];

try {
    // Create database connection
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    switch ($method) {
        case 'GET':
            // Get all reminders for user
            $stmt = $pdo->prepare("
                SELECT r.id, r.title, r.description, r.reminder_time, r.is_recurring, 
                       r.recurrence_pattern, r.status, r.created_at, r.updated_at,
                       t.title as task_title
                FROM reminders r
                LEFT JOIN tasks t ON r.task_id = t.id
                WHERE r.user_id = ? 
                ORDER BY r.reminder_time ASC
            ");
            $stmt->execute([$user_id]);
            $reminders = $stmt->fetchAll();
            
            echo json_encode([
                'success' => true,
                'reminders' => $reminders,
                'count' => count($reminders)
            ]);
            break;
            
        case 'POST':
            // Create new reminder
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            if (empty($data['title']) || empty($data['reminder_time'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Title and reminder time are required']);
                exit();
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO reminders (user_id, task_id, title, description, reminder_time, is_recurring, recurrence_pattern, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $user_id,
                $data['task_id'] ?? null,
                $data['title'],
                $data['description'] ?? '',
                $data['reminder_time'],
                $data['is_recurring'] ?? false,
                $data['recurrence_pattern'] ?? null,
                $data['status'] ?? 'active'
            ]);
            
            $reminder_id = $pdo->lastInsertId();
            
            // Log activity
            $log_stmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address) 
                VALUES (?, 'create', 'reminder', ?, ?, ?)
            ");
            $log_stmt->execute([
                $user_id,
                $reminder_id,
                json_encode(['title' => $data['title']]),
                $_SERVER['REMOTE_ADDR'] ?? ''
            ]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Reminder created successfully',
                'reminder_id' => $reminder_id
            ]);
            break;
            
        case 'PUT':
            // Update reminder
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            if (empty($data['id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Reminder ID is required']);
                exit();
            }
            
            // Check if reminder belongs to user
            $check_stmt = $pdo->prepare("SELECT id FROM reminders WHERE id = ? AND user_id = ?");
            $check_stmt->execute([$data['id'], $user_id]);
            
            if (!$check_stmt->fetch()) {
                http_response_code(404);
                echo json_encode(['error' => 'Reminder not found']);
                exit();
            }
            
            $stmt = $pdo->prepare("
                UPDATE reminders 
                SET task_id = ?, title = ?, description = ?, reminder_time = ?, 
                    is_recurring = ?, recurrence_pattern = ?, status = ?
                WHERE id = ? AND user_id = ?
            ");
            
            $stmt->execute([
                $data['task_id'] ?? null,
                $data['title'],
                $data['description'] ?? '',
                $data['reminder_time'],
                $data['is_recurring'] ?? false,
                $data['recurrence_pattern'] ?? null,
                $data['status'] ?? 'active',
                $data['id'],
                $user_id
            ]);
            
            // Log activity
            $log_stmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address) 
                VALUES (?, 'update', 'reminder', ?, ?, ?)
            ");
            $log_stmt->execute([
                $user_id,
                $data['id'],
                json_encode(['title' => $data['title'], 'status' => $data['status']]),
                $_SERVER['REMOTE_ADDR'] ?? ''
            ]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Reminder updated successfully'
            ]);
            break;
            
        case 'DELETE':
            // Delete reminder
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            if (empty($data['id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Reminder ID is required']);
                exit();
            }
            
            // Check if reminder belongs to user and get title for logging
            $check_stmt = $pdo->prepare("SELECT title FROM reminders WHERE id = ? AND user_id = ?");
            $check_stmt->execute([$data['id'], $user_id]);
            $reminder = $check_stmt->fetch();
            
            if (!$reminder) {
                http_response_code(404);
                echo json_encode(['error' => 'Reminder not found']);
                exit();
            }
            
            $stmt = $pdo->prepare("DELETE FROM reminders WHERE id = ? AND user_id = ?");
            $stmt->execute([$data['id'], $user_id]);
            
            // Log activity
            $log_stmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address) 
                VALUES (?, 'delete', 'reminder', ?, ?, ?)
            ");
            $log_stmt->execute([
                $user_id,
                $data['id'],
                json_encode(['title' => $reminder['title']]),
                $_SERVER['REMOTE_ADDR'] ?? ''
            ]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Reminder deleted successfully'
            ]);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Database error',
        'message' => $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error',
        'message' => $e->getMessage()
    ]);
}
?>
