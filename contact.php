<?php 	
	include("bootfile.php"); 
	$contact_class = CLASS_PATH.'Contact.Class.php';	require_once((string)$contact_class);	unset($contact_class);
	$contact_ob = new Contact();
	
	if(!empty($_POST)){
		$contact_ob->add_contact($_POST);		
		//header('location:contact.php');
	}
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="delete"){
		$contact_ob->delete_contact($_REQUEST['id']);
		header('location:contact.php');
	}	
?>
<?php include(INC_PATH."header.php"); ?>

<div class="container mt-2">
  <h3>Your feedback is important for us.</h3>
  <form action="#" method="POST">
  <div class="row mt-3">
		<div class="col">
			<label for="name">Name</label>
			<input type="text" class="form-control" placeholder="Enter your Name" name="name" required="required" minlength="3" maxlength="20" />
		</div>
		<div class="col">
			<label for="name">Mobile No</label>
			<input type="number" class="form-control" placeholder="Enter your Mobile" name="mobile_no" required="required" min="6276957193" max="9999999999" />
		</div>
	</div>
	<div class="row mt-3">
	<div class="col">
		<label for="Message">Please Describred Message</label>
		<textarea class="form-control" rows="4" name="message"></textarea>			
		<div class="col">
	</div>
	<div class="row p-4">
		<button type="submit" class="btn btn-primary btn-block">Submit</button>
	</div>
  </form>
  <?php if($_SESSION['user_type']=="Boss"){ ?> 
  <h2>View All Messaages</h2>
  <p>Use feedback to improvement:</p>
  <?php $rows = $contact_ob->view_contact_all(); $k=0; ?>  
  <table class="table table-striped">
    <thead>
      <tr>
        <th>No</th>
        <th>Name</th>
		<th>Mobile No</th> 
		<th>Messsage</th>  
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
	<?php foreach($rows as $row){ $k++; ?>
      <tr>
        <td><?=$k?></td>
        <td><?=$row["name"]?></td>
		<td><?=$row["mobile_no"]?></td>
		<td><?=$row["message"]?></td>
        <td><a href="?action=delete&id=<?=$row['id']?>"><i class="fa fa-times" style="color:red"></i></a></td>
      </tr>
	<?php } ?>
    </tbody>
  </table>
  <?php } ?>
</div>
<?php include(INC_PATH."footer.php"); ?>