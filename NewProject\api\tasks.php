<?php
/**
 * Tasks API Endpoints
 * RESTful API for task management
 */

require_once '../config/config.php';

$method = $_SERVER['REQUEST_METHOD'];
$request_uri = $_SERVER['REQUEST_URI'];
$path_parts = explode('/', trim(parse_url($request_uri, PHP_URL_PATH), '/'));

// Get task ID if provided
$task_id = isset($path_parts[2]) && is_numeric($path_parts[2]) ? (int)$path_parts[2] : null;

switch ($method) {
    case 'GET':
        if ($task_id) {
            getTask($task_id);
        } else {
            getTasks();
        }
        break;
    
    case 'POST':
        createTask();
        break;
    
    case 'PUT':
        if ($task_id) {
            updateTask($task_id);
        } else {
            jsonResponse(['error' => 'Task ID is required'], 400);
        }
        break;
    
    case 'DELETE':
        if ($task_id) {
            deleteTask($task_id);
        } else {
            jsonResponse(['error' => 'Task ID is required'], 400);
        }
        break;
    
    default:
        jsonResponse(['error' => 'Method not allowed'], 405);
        break;
}

function getTasks() {
    requireAuth();
    
    $database = new Database();
    $db = $database->getConnection();
    $task = new Task($db);
    
    // Check if admin wants to see all tasks
    if (isAdmin() && isset($_GET['all']) && $_GET['all'] === 'true') {
        $stmt = $task->readAll();
    } else {
        $stmt = $task->readByUser($_SESSION['user_id']);
    }
    
    $tasks = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $tasks[] = $row;
    }
    
    jsonResponse([
        'success' => true,
        'tasks' => $tasks,
        'count' => count($tasks)
    ]);
}

function getTask($task_id) {
    requireAuth();
    
    $database = new Database();
    $db = $database->getConnection();
    $task = new Task($db);
    $task->id = $task_id;
    
    if ($task->readOne()) {
        // Check if user owns this task or is admin
        if ($task->user_id != $_SESSION['user_id'] && !isAdmin()) {
            jsonResponse(['error' => 'Access denied'], 403);
        }
        
        jsonResponse([
            'success' => true,
            'task' => [
                'id' => $task->id,
                'user_id' => $task->user_id,
                'title' => $task->title,
                'description' => $task->description,
                'priority' => $task->priority,
                'status' => $task->status,
                'due_date' => $task->due_date,
                'created_at' => $task->created_at,
                'updated_at' => $task->updated_at
            ]
        ]);
    } else {
        jsonResponse(['error' => 'Task not found'], 404);
    }
}

function createTask() {
    requireAuth();
    
    $data = json_decode(file_get_contents("php://input"), true);
    
    // Validate required fields
    if (empty($data['title'])) {
        jsonResponse(['error' => 'Title is required'], 400);
    }
    
    // Validate priority
    $valid_priorities = ['low', 'medium', 'high'];
    if (isset($data['priority']) && !in_array($data['priority'], $valid_priorities)) {
        jsonResponse(['error' => 'Invalid priority. Must be: low, medium, or high'], 400);
    }
    
    // Validate status
    $valid_statuses = ['pending', 'in_progress', 'completed'];
    if (isset($data['status']) && !in_array($data['status'], $valid_statuses)) {
        jsonResponse(['error' => 'Invalid status. Must be: pending, in_progress, or completed'], 400);
    }
    
    // Validate due_date format
    if (isset($data['due_date']) && !empty($data['due_date'])) {
        $date = DateTime::createFromFormat('Y-m-d H:i:s', $data['due_date']);
        if (!$date) {
            jsonResponse(['error' => 'Invalid due_date format. Use: YYYY-MM-DD HH:MM:SS'], 400);
        }
    }
    
    $database = new Database();
    $db = $database->getConnection();
    $task = new Task($db);
    
    // Set task properties
    $task->user_id = $_SESSION['user_id'];
    $task->title = $data['title'];
    $task->description = $data['description'] ?? '';
    $task->priority = $data['priority'] ?? 'medium';
    $task->status = $data['status'] ?? 'pending';
    $task->due_date = $data['due_date'] ?? null;
    
    if ($task->create()) {
        jsonResponse([
            'success' => true,
            'message' => 'Task created successfully',
            'task_id' => $task->id
        ], 201);
    } else {
        jsonResponse(['error' => 'Failed to create task'], 500);
    }
}

function updateTask($task_id) {
    requireAuth();
    
    $data = json_decode(file_get_contents("php://input"), true);
    
    $database = new Database();
    $db = $database->getConnection();
    $task = new Task($db);
    $task->id = $task_id;
    
    if (!$task->readOne()) {
        jsonResponse(['error' => 'Task not found'], 404);
    }
    
    // Check if user owns this task or is admin
    if ($task->user_id != $_SESSION['user_id'] && !isAdmin()) {
        jsonResponse(['error' => 'Access denied'], 403);
    }
    
    // Validate priority if provided
    if (isset($data['priority'])) {
        $valid_priorities = ['low', 'medium', 'high'];
        if (!in_array($data['priority'], $valid_priorities)) {
            jsonResponse(['error' => 'Invalid priority. Must be: low, medium, or high'], 400);
        }
    }
    
    // Validate status if provided
    if (isset($data['status'])) {
        $valid_statuses = ['pending', 'in_progress', 'completed'];
        if (!in_array($data['status'], $valid_statuses)) {
            jsonResponse(['error' => 'Invalid status. Must be: pending, in_progress, or completed'], 400);
        }
    }
    
    // Validate due_date format if provided
    if (isset($data['due_date']) && !empty($data['due_date'])) {
        $date = DateTime::createFromFormat('Y-m-d H:i:s', $data['due_date']);
        if (!$date) {
            jsonResponse(['error' => 'Invalid due_date format. Use: YYYY-MM-DD HH:MM:SS'], 400);
        }
    }
    
    // Update task properties
    $task->title = $data['title'] ?? $task->title;
    $task->description = $data['description'] ?? $task->description;
    $task->priority = $data['priority'] ?? $task->priority;
    $task->status = $data['status'] ?? $task->status;
    $task->due_date = isset($data['due_date']) ? $data['due_date'] : $task->due_date;
    
    if ($task->update()) {
        jsonResponse([
            'success' => true,
            'message' => 'Task updated successfully'
        ]);
    } else {
        jsonResponse(['error' => 'Failed to update task'], 500);
    }
}

function deleteTask($task_id) {
    requireAuth();
    
    $database = new Database();
    $db = $database->getConnection();
    $task = new Task($db);
    $task->id = $task_id;
    
    if (!$task->readOne()) {
        jsonResponse(['error' => 'Task not found'], 404);
    }
    
    // Check if user owns this task or is admin
    if ($task->user_id != $_SESSION['user_id'] && !isAdmin()) {
        jsonResponse(['error' => 'Access denied'], 403);
    }
    
    if ($task->delete()) {
        jsonResponse([
            'success' => true,
            'message' => 'Task deleted successfully'
        ]);
    } else {
        jsonResponse(['error' => 'Failed to delete task'], 500);
    }
}
?>
