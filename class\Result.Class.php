<?php
class Result extends PaperSet{
	public $table = 'result';
	// for exam save
	public function add_result($post){
		$row['paper_id']=$post['paper_id']; unset($post['paper_id']);
		$row['start_time']=$post['start_time']; unset($post['start_time']);
		$row['user_id']=$_SESSION['user_id'];
		$this->add_attempt($row); unset($row['start_time']);
		$row['attempt_id']=$this->last_inser_id;
		$this->check_attempt($row['paper_id'],$row['attempt_id'],$row['user_id']);		
		foreach($post as $field => $value){
			$row['paperset_id'] = substr($field,3);
			$row['question_id'] = parent::get_question_id($row['paperset_id']);
			$row['answer'] = $value;
			$query = parent::insert_record($this->table,$row);
			$data = parent::update_query($query);
		}
		header('location:result.php?attemptid='.$row['attempt_id'].'&paperid='.$row['paper_id']);
	}
	public function add_attempt($post){
		$query = parent::insert_record('paper_attempt',$post);
		$data = parent::update_query($query);
	}	
	public function check_attempt($paper_id,$attempt_id,$userid){
		if($this->select_attend_question_of_paper($paper_id,$attempt_id,$userid)>0){
			$this->delete_old_attempt($paper_id,$userid);	
		};
	}
	public function delete_old_attempt($paperid,$user_id){
		$query = "DELETE FROM `result` WHERE `paper_id` = ".$paperid." AND `user_id` =". $user_id;
		$result = parent::update_query($query);
	}	
	// used in rrsiltlist2.php
	public function view_all_attempt_with_student($user_id){
		$query = "SELECT `paper`.`name` AS `name`,`paper`.`id` AS `paper_id`,`user`.`name` AS `studentname`,`paper_attempt`.`id` AS `attempt_id`,`paper_attempt`.`start_time` AS `attempt_date` FROM  `user` JOIN `paper` JOIN `paper_attempt` ON `paper`.`id`=`paper_attempt`.`paper_id` AND `paper_attempt`.`user_id`=`user`.`id` WHERE `paper`.`user_id`=".$user_id." ORDER BY `paper_attempt`.`id` DESC";
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function view_user_attempt($user_id){
		$query = "SELECT * FROM `paper` JOIN `paper_attempt` ON `paper`.`id`=`paper_attempt`.`paper_id` WHERE `paper_attempt`.`user_id` =".$user_id." ORDER BY `paper_attempt`.`id` DESC";
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}	
	public function select_attend_question_of_paper($paperid,$attemptid){
		$query = "SELECT count(*) FROM `result` WHERE `paper_id` = ".$paperid." AND `attempt_id`=$attemptid";		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array[0][0];
	}
	public function select_right_answer_of_paper($paperid,$attemptid){
		$query = "SELECT count(*) FROM `result` JOIN `question` ON `result`.`question_id`=`question`.`id` WHERE `paper_id` = ".$paperid." AND `attempt_id`=".$attemptid." AND `result`.`answer`=`question`.`ans`";
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array[0][0];
	}
	//result.php used this function
	public function select_submited_answer_of_paper($attemptid){
		$query = "SELECT * FROM `result` WHERE `attempt_id`=".$attemptid;		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
}
?>