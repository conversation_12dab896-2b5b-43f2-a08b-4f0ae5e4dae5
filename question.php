<?php include("bootfile.php"); 
	$user_ob->user_page();
	$question_class = CLASS_PATH.'Question.Class.php';	require_once((string)$question_class);	unset($question_class);
	$question_ob = new Question();
	
	if(!empty($_POST) and isset($_POST['id'])){
		$question_ob->update_question($_POST);
		header('location:question.php');
	}
	else if(!empty($_POST)){
		$question_ob->add_question($_POST);
		header('location:question.php');
	}
	else{ }
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="delete"){
		$question_ob->delete_question($_REQUEST['id']);
		header('location:question.php');
	}
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){
		$row = $question_ob->select_question_id($_REQUEST['id']);
		$_POST=$row[0];
	} 
?>
<?php include(INC_PATH."header.php"); ?>
<div class="container">
  <h3>Question </h3>
  <p>Only Question add in databse.</p>
  <form action="#" method="POST">
	<div class="row m-1"> 
		<div class="row">
			<label for="que">Question</label>
			<textarea class="form-control" rows="5" name="que"><?php if(!empty($_POST['que'])){ echo $_POST['que']; } ?></textarea>			
		</div>
		<div class="row">
			<label for="Opt1">Option 1</label>
			<textarea class="form-control" rows="2" name="opt1"><?php if(!empty($_POST['opt1'])){ echo $_POST['opt1']; } ?></textarea>
		</div>
		<div class="row">
			<label for="opt2">Option 2</label>
			<textarea class="form-control" rows="2" name="opt2"><?php if(!empty($_POST['opt2'])){ echo $_POST['opt2']; } ?></textarea>
		</div>
		<div class="row">
			<label for="opt3">Option 3</label>
			<textarea class="form-control" rows="2" name="opt3"><?php if(!empty($_POST['opt3'])){ echo $_POST['opt3']; } ?></textarea>
		</div>
		<div class="row">
			<label for="opt4">Option 4</label>
			<textarea class="form-control" rows="2" name="opt4"><?php if(!empty($_POST['opt4'])){ echo $_POST['opt4']; } ?></textarea>
		</div>
		<div class="row">
			<label for="opt5">Option 5</label>
			<textarea class="form-control" rows="2" name="opt5"><?php if(!empty($_POST['opt5'])){ echo $_POST['opt5']; } ?></textarea>
		</div>
		<div class="row">
			<label for="ans">Answer</label>
			<textarea class="form-control" rows="2" name="ans"><?php if(!empty($_POST['ans'])){ echo $_POST['ans']; } ?></textarea>
		</div>
		<?php if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){ ?>
			<input type="hidden" name="id" value="<?=$_POST['id']?>">
		<?php } ?>	
	<div class="row p-4">
		<button type="submit" class="btn btn-primary btn-block">Submit</button>
	</div>
  </form>

  <h2>View All Questions</h2>
  <p>Must check answer it must be same as option.</p>
  <?php $rows = $question_ob->view_question_all(); $k=0; ?>
  <div class="table-responsive">
  <table id="<?=$page_name?>" class="table table-striped">
    <thead style="position: sticky;top: 0">
      <tr>
        <th>No</th>
        <th>Question</th>
		<th>Option 1</th>
		<th>Option 2</th>
		<th>Option 3</th>
		<th>Option 4</th>
		<th>Option 5</th>
		<th>Answer</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
	<?php foreach($rows as $row){ $k++; ?>
      <tr>
        <td><?=$k?></td>
        <td><?=$row["que"]?></td>
		<td><?=$row["opt1"]?></td>
		<td><?=$row["opt2"]?></td>
		<td><?=$row["opt3"]?></td>
		<td><?=$row["opt4"]?></td>
		<td><?=$row["opt5"]?></td>
		<td><?=$row["ans"]?></td>
        <td>
			<a href="?action=edit&id=<?=$row['id']?>"><i class="fa fa-edit"></i></a>
			<a href="?action=delete&id=<?=$row['id']?>"><i class="fa fa-times" style="color:red"></i></a>
		</td>
      </tr>
	<?php } ?>
    </tbody>
  </table>
  </div>
</div>
<?php include(INC_PATH."footer.php"); ?>