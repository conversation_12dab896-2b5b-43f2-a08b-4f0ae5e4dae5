<?php include("bootfile.php"); 
    $user_ob->user_page();
    $question_class = CLASS_PATH.'Question.Class.php';	require_once((string)$question_class);	unset($question_class);
		$question_ob = new Question();
    $paperset_class = CLASS_PATH.'PaperSet.Class.php';	require_once((string)$paperset_class);	unset($paperset_class);
		$paperset_ob = new PaperSet();
    $result_class = CLASS_PATH.'Result.Class.php';	require_once((string)$result_class);	unset($result_class);
    $result_ob = new Result(); ?>
<?php include(INC_PATH."header.php"); ?>
<div class="container mt-2">
<h2>View Result with student  name</h2>
  <p>List all paper with student name:</p>
  <div class="table-responsive">
  <table id="<?=$page_name?>" class="table table-striped">
    <thead>
      <tr>
        <th>No</th>
        <th>Paper Name</th>
        <th>Student Name</th>
        <th>Date</th>
        <th>Total Que.</th>
        <th>Attend Que.</th>
        <th>Right Ans.</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
    <?php $rows = $result_ob->view_all_attempt_with_student($_SESSION['user_id']); $k=0; ?>    
	  <?php foreach($rows as $row){ $k++; ?>
      <tr>
        <td><?=$k?></td>
        <td><?=$row["name"]?></td>
        <td><?=$row["studentname"]?></td>
        <td><?=date('d-M-y',strtotime($row["attempt_date"]))?></td>
        <td><?=$paperset_ob->select_paperset_no_of_question($row['paper_id'])?></td>
        <td><?=$result_ob->select_attend_question_of_paper($row['paper_id'],$row['attempt_id'])?></td>
        <td><?=$result_ob->select_right_answer_of_paper($row['paper_id'],$row['attempt_id'])?></td>
        <td><a href="result.php?attemptid=<?=$row['attempt_id']?>&paperid=<?=$row['paper_id']?>"><i class="fa fa-paste" title="View"></i></a></td>
      </tr>
	  <?php } ?>
    </tbody>
  </table>
  </div>
</div>
<?php include(INC_PATH . "footer.php"); ?>
