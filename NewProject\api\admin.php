<?php
/**
 * Admin API Endpoints
 * Admin-only functionality for user management and activity monitoring
 */

require_once '../config/config.php';

$method = $_SERVER['REQUEST_METHOD'];
$request_uri = $_SERVER['REQUEST_URI'];
$path_parts = explode('/', trim(parse_url($request_uri, PHP_URL_PATH), '/'));

// Get the action from URL
$action = isset($path_parts[2]) ? $path_parts[2] : '';
$id = isset($path_parts[3]) && is_numeric($path_parts[3]) ? (int)$path_parts[3] : null;

// Require admin access for all endpoints
requireAdmin();

switch ($method) {
    case 'GET':
        if ($action === 'users') {
            if ($id) {
                getUser($id);
            } else {
                getUsers();
            }
        } elseif ($action === 'activities') {
            getActivities();
        } elseif ($action === 'statistics') {
            getStatistics();
        } elseif ($action === 'dashboard') {
            getDashboard();
        } else {
            jsonResponse(['error' => 'Invalid endpoint'], 404);
        }
        break;
    
    case 'POST':
        if ($action === 'users') {
            createUser();
        } else {
            jsonResponse(['error' => 'Invalid endpoint'], 404);
        }
        break;
    
    case 'PUT':
        if ($action === 'users' && $id) {
            updateUser($id);
        } else {
            jsonResponse(['error' => 'Invalid endpoint or missing ID'], 400);
        }
        break;
    
    case 'DELETE':
        if ($action === 'users' && $id) {
            deleteUser($id);
        } else {
            jsonResponse(['error' => 'Invalid endpoint or missing ID'], 400);
        }
        break;
    
    default:
        jsonResponse(['error' => 'Method not allowed'], 405);
        break;
}

function getUsers() {
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);
    
    $stmt = $user->readAll();
    $users = [];
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $users[] = $row;
    }
    
    jsonResponse([
        'success' => true,
        'users' => $users,
        'count' => count($users)
    ]);
}

function getUser($user_id) {
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);
    $user->id = $user_id;
    
    if ($user->readOne()) {
        // Get user statistics
        $task = new Task($db);
        $task_stats = $task->getStatistics($user_id);
        
        $reminder = new Reminder($db);
        $upcoming_reminders = $reminder->getUpcoming($user_id, 24);
        $upcoming_count = 0;
        while ($upcoming_reminders->fetch()) {
            $upcoming_count++;
        }
        
        jsonResponse([
            'success' => true,
            'user' => [
                'id' => $user->id,
                'username' => $user->username,
                'email' => $user->email,
                'full_name' => $user->full_name,
                'role' => $user->role,
                'status' => $user->status,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at
            ],
            'statistics' => [
                'tasks' => $task_stats,
                'upcoming_reminders' => $upcoming_count
            ]
        ]);
    } else {
        jsonResponse(['error' => 'User not found'], 404);
    }
}

function createUser() {
    $data = json_decode(file_get_contents("php://input"), true);
    
    // Validate required fields
    $required_fields = ['username', 'email', 'password', 'full_name'];
    foreach ($required_fields as $field) {
        if (empty($data[$field])) {
            jsonResponse(['error' => ucfirst($field) . ' is required'], 400);
        }
    }
    
    // Validate email format
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        jsonResponse(['error' => 'Invalid email format'], 400);
    }
    
    // Validate role
    $valid_roles = ['user', 'admin'];
    if (isset($data['role']) && !in_array($data['role'], $valid_roles)) {
        jsonResponse(['error' => 'Invalid role. Must be: user or admin'], 400);
    }
    
    // Validate status
    $valid_statuses = ['active', 'inactive'];
    if (isset($data['status']) && !in_array($data['status'], $valid_statuses)) {
        jsonResponse(['error' => 'Invalid status. Must be: active or inactive'], 400);
    }
    
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);
    
    // Set user properties
    $user->username = $data['username'];
    $user->email = $data['email'];
    $user->password = $data['password'];
    $user->full_name = $data['full_name'];
    $user->role = $data['role'] ?? 'user';
    $user->status = $data['status'] ?? 'active';
    
    // Check if user already exists
    if ($user->userExists()) {
        jsonResponse(['error' => 'Username or email already exists'], 409);
    }
    
    if ($user->create()) {
        jsonResponse([
            'success' => true,
            'message' => 'User created successfully',
            'user_id' => $user->id
        ], 201);
    } else {
        jsonResponse(['error' => 'Failed to create user'], 500);
    }
}

function updateUser($user_id) {
    $data = json_decode(file_get_contents("php://input"), true);
    
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);
    $user->id = $user_id;
    
    if (!$user->readOne()) {
        jsonResponse(['error' => 'User not found'], 404);
    }
    
    // Validate email format if provided
    if (isset($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        jsonResponse(['error' => 'Invalid email format'], 400);
    }
    
    // Validate role if provided
    if (isset($data['role'])) {
        $valid_roles = ['user', 'admin'];
        if (!in_array($data['role'], $valid_roles)) {
            jsonResponse(['error' => 'Invalid role. Must be: user or admin'], 400);
        }
    }
    
    // Validate status if provided
    if (isset($data['status'])) {
        $valid_statuses = ['active', 'inactive'];
        if (!in_array($data['status'], $valid_statuses)) {
            jsonResponse(['error' => 'Invalid status. Must be: active or inactive'], 400);
        }
    }
    
    // Update user properties
    $user->username = $data['username'] ?? $user->username;
    $user->email = $data['email'] ?? $user->email;
    $user->full_name = $data['full_name'] ?? $user->full_name;
    $user->role = $data['role'] ?? $user->role;
    $user->status = $data['status'] ?? $user->status;
    
    if ($user->update()) {
        jsonResponse([
            'success' => true,
            'message' => 'User updated successfully'
        ]);
    } else {
        jsonResponse(['error' => 'Failed to update user'], 500);
    }
}

function deleteUser($user_id) {
    // Prevent admin from deleting themselves
    if ($user_id == $_SESSION['user_id']) {
        jsonResponse(['error' => 'Cannot delete your own account'], 400);
    }
    
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);
    $user->id = $user_id;
    
    if (!$user->readOne()) {
        jsonResponse(['error' => 'User not found'], 404);
    }
    
    if ($user->delete()) {
        jsonResponse([
            'success' => true,
            'message' => 'User deleted successfully'
        ]);
    } else {
        jsonResponse(['error' => 'Failed to delete user'], 500);
    }
}

function getActivities() {
    $database = new Database();
    $db = $database->getConnection();
    $activity_log = new ActivityLog($db);
    
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
    $user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : null;
    $entity_type = isset($_GET['entity_type']) ? $_GET['entity_type'] : null;
    $search = isset($_GET['search']) ? $_GET['search'] : null;
    
    if ($search) {
        $stmt = $activity_log->search($search, $limit, $offset);
    } elseif ($user_id) {
        $stmt = $activity_log->readByUser($user_id, $limit, $offset);
    } elseif ($entity_type) {
        $stmt = $activity_log->readByEntityType($entity_type, $limit, $offset);
    } else {
        $stmt = $activity_log->readAll($limit, $offset);
    }
    
    $activities = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $row['details'] = json_decode($row['details'], true);
        $activities[] = $row;
    }
    
    jsonResponse([
        'success' => true,
        'activities' => $activities,
        'count' => count($activities)
    ]);
}

function getStatistics() {
    $database = new Database();
    $db = $database->getConnection();
    $activity_log = new ActivityLog($db);
    
    $days = isset($_GET['days']) ? (int)$_GET['days'] : 30;
    
    $stats = $activity_log->getStatistics($days);
    $daily_chart = $activity_log->getDailyActivityChart(7);
    $most_active = $activity_log->getMostActiveUsers(10, $days);
    
    $chart_data = [];
    while ($row = $daily_chart->fetch(PDO::FETCH_ASSOC)) {
        $chart_data[] = $row;
    }
    
    $active_users = [];
    while ($row = $most_active->fetch(PDO::FETCH_ASSOC)) {
        $active_users[] = $row;
    }
    
    jsonResponse([
        'success' => true,
        'statistics' => $stats,
        'daily_activity_chart' => $chart_data,
        'most_active_users' => $active_users
    ]);
}

function getDashboard() {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get overall statistics
    $activity_log = new ActivityLog($db);
    $stats = $activity_log->getStatistics(30);
    $recent_activities = $activity_log->getRecentActivities(10);
    
    // Get user count
    $user = new User($db);
    $user_stmt = $user->readAll();
    $total_users = 0;
    while ($user_stmt->fetch()) {
        $total_users++;
    }
    
    // Get task statistics
    $task = new Task($db);
    $task_stmt = $task->readAll();
    $total_tasks = 0;
    $completed_tasks = 0;
    while ($row = $task_stmt->fetch(PDO::FETCH_ASSOC)) {
        $total_tasks++;
        if ($row['status'] === 'completed') {
            $completed_tasks++;
        }
    }
    
    // Get reminder statistics
    $reminder = new Reminder($db);
    $reminder_stmt = $reminder->readAll();
    $total_reminders = 0;
    $active_reminders = 0;
    while ($row = $reminder_stmt->fetch(PDO::FETCH_ASSOC)) {
        $total_reminders++;
        if ($row['status'] === 'active') {
            $active_reminders++;
        }
    }
    
    $activities = [];
    while ($row = $recent_activities->fetch(PDO::FETCH_ASSOC)) {
        $row['details'] = json_decode($row['details'], true);
        $activities[] = $row;
    }
    
    jsonResponse([
        'success' => true,
        'dashboard' => [
            'total_users' => $total_users,
            'total_tasks' => $total_tasks,
            'completed_tasks' => $completed_tasks,
            'total_reminders' => $total_reminders,
            'active_reminders' => $active_reminders,
            'activity_stats' => $stats,
            'recent_activities' => $activities
        ]
    ]);
}
?>
