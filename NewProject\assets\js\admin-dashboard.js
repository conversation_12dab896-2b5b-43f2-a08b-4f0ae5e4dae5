// Admin Dashboard JavaScript
const API_BASE = '../api';
let currentUser = null;

// Initialize admin dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication and admin role
    checkAdminAuth();
    
    // Initialize event listeners
    initializeEventListeners();
    
    // Load dashboard data
    loadAdminDashboardData();
});

// Check if user is authenticated and has admin role
function checkAdminAuth() {
    const user = localStorage.getItem('user');
    if (!user) {
        window.location.href = 'login.html';
        return;
    }
    
    currentUser = JSON.parse(user);
    
    if (currentUser.role !== 'admin') {
        alert('Access denied. Admin privileges required.');
        window.location.href = 'dashboard.html';
        return;
    }
    
    document.getElementById('adminWelcome').textContent = `Welcome, ${currentUser.full_name}!`;
}

// Initialize event listeners
function initializeEventListeners() {
    // Navigation
    document.querySelectorAll('[data-section]').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            showSection(section);
            
            // Update active nav
            document.querySelectorAll('.nav-link').forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Logout
    document.getElementById('logoutBtn').addEventListener('click', logout);
    
    // User form
    document.getElementById('saveUserBtn').addEventListener('click', saveUser);
    
    // Search and filters
    document.getElementById('userSearch').addEventListener('input', searchUsers);
    document.getElementById('activityFilter').addEventListener('change', filterActivities);
    document.getElementById('activitySearch').addEventListener('input', searchActivities);
}

// Show specific section
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // Show selected section
    document.getElementById(`${sectionName}-section`).style.display = 'block';
    
    // Update page title
    const titles = {
        'overview': 'Admin Dashboard',
        'users': 'User Management',
        'activities': 'Activity Monitor',
        'statistics': 'Statistics',
        'tasks': 'All Tasks',
        'reminders': 'All Reminders'
    };
    document.getElementById('pageTitle').textContent = titles[sectionName] || 'Admin Dashboard';
    
    // Load section-specific data
    switch(sectionName) {
        case 'users':
            loadUsers();
            break;
        case 'activities':
            loadActivities();
            break;
        case 'statistics':
            loadStatistics();
            break;
        case 'tasks':
            loadAllTasks();
            break;
        case 'reminders':
            loadAllReminders();
            break;
    }
}

// Load admin dashboard overview data
async function loadAdminDashboardData() {
    try {
        const response = await fetch(`${API_BASE}/admin/dashboard`);
        const data = await response.json();
        
        if (data.success) {
            updateAdminStats(data.dashboard);
            displayRecentActivities(data.dashboard.recent_activities);
            displaySystemOverview(data.dashboard);
        }
    } catch (error) {
        console.error('Error loading admin dashboard data:', error);
        showAlert('Error loading dashboard data', 'danger');
    }
}

// Update admin dashboard statistics
function updateAdminStats(dashboard) {
    document.getElementById('totalUsers').textContent = dashboard.total_users || 0;
    document.getElementById('totalTasks').textContent = dashboard.total_tasks || 0;
    document.getElementById('totalReminders').textContent = dashboard.total_reminders || 0;
    document.getElementById('totalActivities').textContent = dashboard.activity_stats?.total_activities || 0;
}

// Display recent activities
function displayRecentActivities(activities) {
    const container = document.getElementById('recentActivities');
    
    if (!activities || activities.length === 0) {
        container.innerHTML = '<p class="text-muted">No recent activities</p>';
        return;
    }
    
    const html = activities.map(activity => `
        <div class="activity-item">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <strong>${activity.username || 'Unknown User'}</strong> 
                    <span class="text-muted">${activity.action}</span> 
                    <span class="badge bg-secondary">${activity.entity_type}</span>
                    ${activity.details ? `<br><small class="text-muted">${JSON.stringify(activity.details)}</small>` : ''}
                </div>
                <small class="text-muted">${formatDateTime(activity.created_at)}</small>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// Display system overview
function displaySystemOverview(dashboard) {
    const container = document.getElementById('systemOverview');
    
    const completionRate = dashboard.total_tasks > 0 ? 
        Math.round((dashboard.completed_tasks / dashboard.total_tasks) * 100) : 0;
    
    const html = `
        <div class="mb-3">
            <div class="d-flex justify-content-between">
                <span>Task Completion Rate</span>
                <span>${completionRate}%</span>
            </div>
            <div class="progress mt-1">
                <div class="progress-bar bg-success" style="width: ${completionRate}%"></div>
            </div>
        </div>
        <div class="mb-3">
            <div class="d-flex justify-content-between">
                <span>Active Reminders</span>
                <span>${dashboard.active_reminders || 0}</span>
            </div>
        </div>
        <div class="mb-3">
            <div class="d-flex justify-content-between">
                <span>Active Users</span>
                <span>${dashboard.activity_stats?.active_users || 0}</span>
            </div>
        </div>
        <div class="mb-3">
            <div class="d-flex justify-content-between">
                <span>Total Logins (30d)</span>
                <span>${dashboard.activity_stats?.total_logins || 0}</span>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

// Load users
async function loadUsers() {
    try {
        const response = await fetch(`${API_BASE}/admin/users`);
        const data = await response.json();
        
        if (data.success) {
            displayUsers(data.users);
        }
    } catch (error) {
        console.error('Error loading users:', error);
        showAlert('Error loading users', 'danger');
    }
}

// Display users in table
function displayUsers(users) {
    const tbody = document.getElementById('usersTableBody');
    
    if (!users || users.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">No users found</td></tr>';
        return;
    }
    
    const html = users.map(user => `
        <tr>
            <td>${user.id}</td>
            <td>${user.username}</td>
            <td>${user.full_name}</td>
            <td>${user.email}</td>
            <td><span class="badge bg-${user.role === 'admin' ? 'danger' : 'primary'}">${user.role}</span></td>
            <td><span class="badge bg-${user.status === 'active' ? 'success' : 'secondary'}">${user.status}</span></td>
            <td>${formatDate(user.created_at)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editUser(${user.id})" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="viewUserDetails(${user.id})" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${user.id !== currentUser.id ? `
                        <button class="btn btn-outline-danger" onclick="deleteUser(${user.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = html;
}

// Load activities
async function loadActivities() {
    try {
        const entityType = document.getElementById('activityFilter').value;
        const search = document.getElementById('activitySearch').value;
        
        let url = `${API_BASE}/admin/activities?limit=50`;
        if (entityType) url += `&entity_type=${entityType}`;
        if (search) url += `&search=${encodeURIComponent(search)}`;
        
        const response = await fetch(url);
        const data = await response.json();
        
        if (data.success) {
            displayActivities(data.activities);
        }
    } catch (error) {
        console.error('Error loading activities:', error);
        showAlert('Error loading activities', 'danger');
    }
}

// Display activities
function displayActivities(activities) {
    const container = document.getElementById('activitiesList');
    
    if (!activities || activities.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No activities found</p>';
        return;
    }
    
    const html = activities.map(activity => `
        <div class="card mb-2">
            <div class="card-body py-2">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <strong>${activity.username || 'Unknown User'}</strong>
                        <br><small class="text-muted">${activity.full_name || ''}</small>
                    </div>
                    <div class="col-md-2">
                        <span class="badge bg-primary">${activity.action}</span>
                    </div>
                    <div class="col-md-2">
                        <span class="badge bg-secondary">${activity.entity_type}</span>
                    </div>
                    <div class="col-md-3">
                        ${activity.details ? `<small>${JSON.stringify(activity.details)}</small>` : ''}
                    </div>
                    <div class="col-md-2 text-end">
                        <small class="text-muted">${formatDateTime(activity.created_at)}</small>
                        <br><small class="text-muted">${activity.ip_address || ''}</small>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// Load statistics
async function loadStatistics() {
    try {
        const response = await fetch(`${API_BASE}/admin/statistics`);
        const data = await response.json();
        
        if (data.success) {
            displayActivityStats(data.statistics);
            displayMostActiveUsers(data.most_active_users);
        }
    } catch (error) {
        console.error('Error loading statistics:', error);
        showAlert('Error loading statistics', 'danger');
    }
}

// Display activity statistics
function displayActivityStats(stats) {
    const container = document.getElementById('activityStats');
    
    const html = `
        <div class="row text-center">
            <div class="col-6 mb-3">
                <h4 class="text-primary">${stats.total_activities || 0}</h4>
                <small>Total Activities</small>
            </div>
            <div class="col-6 mb-3">
                <h4 class="text-success">${stats.active_users || 0}</h4>
                <small>Active Users</small>
            </div>
            <div class="col-6 mb-3">
                <h4 class="text-info">${stats.total_logins || 0}</h4>
                <small>Total Logins</small>
            </div>
            <div class="col-6 mb-3">
                <h4 class="text-warning">${stats.task_activities || 0}</h4>
                <small>Task Activities</small>
            </div>
        </div>
        <hr>
        <div class="row text-center">
            <div class="col-6">
                <h5 class="text-primary">${stats.reminder_activities || 0}</h5>
                <small>Reminder Activities</small>
            </div>
            <div class="col-6">
                <h5 class="text-secondary">${stats.user_activities || 0}</h5>
                <small>User Activities</small>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

// Display most active users
function displayMostActiveUsers(users) {
    const container = document.getElementById('activeUsers');
    
    if (!users || users.length === 0) {
        container.innerHTML = '<p class="text-muted">No active users data</p>';
        return;
    }
    
    const html = users.map((user, index) => `
        <div class="d-flex justify-content-between align-items-center py-2 ${index < users.length - 1 ? 'border-bottom' : ''}">
            <div>
                <strong>${user.username}</strong>
                <br><small class="text-muted">${user.full_name}</small>
            </div>
            <div class="text-end">
                <span class="badge bg-primary">${user.activity_count} activities</span>
                <br><small class="text-muted">${formatDate(user.last_activity)}</small>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// Load all tasks
async function loadAllTasks() {
    try {
        const response = await fetch(`${API_BASE}/tasks?all=true`);
        const data = await response.json();
        
        if (data.success) {
            displayAllTasks(data.tasks);
        }
    } catch (error) {
        console.error('Error loading tasks:', error);
        showAlert('Error loading tasks', 'danger');
    }
}

// Display all tasks
function displayAllTasks(tasks) {
    const container = document.getElementById('allTasksList');
    
    if (!tasks || tasks.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No tasks found</p>';
        return;
    }
    
    const html = tasks.map(task => `
        <div class="card mb-3">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="card-title">${task.title}</h6>
                        <p class="card-text text-muted">${task.description || 'No description'}</p>
                        <div class="d-flex gap-2">
                            <span class="badge bg-${getPriorityColor(task.priority)}">${task.priority}</span>
                            <span class="badge bg-${getStatusColor(task.status)}">${task.status.replace('_', ' ')}</span>
                            ${task.due_date ? `<span class="badge bg-secondary">${formatDate(task.due_date)}</span>` : ''}
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <small class="text-muted">
                            User: ${task.username || 'Unknown'}<br>
                            Created: ${formatDate(task.created_at)}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// Load all reminders
async function loadAllReminders() {
    try {
        const response = await fetch(`${API_BASE}/reminders?all=true`);
        const data = await response.json();
        
        if (data.success) {
            displayAllReminders(data.reminders);
        }
    } catch (error) {
        console.error('Error loading reminders:', error);
        showAlert('Error loading reminders', 'danger');
    }
}

// Display all reminders
function displayAllReminders(reminders) {
    const container = document.getElementById('allRemindersList');
    
    if (!reminders || reminders.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No reminders found</p>';
        return;
    }
    
    const html = reminders.map(reminder => `
        <div class="card mb-3">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="card-title">${reminder.title}</h6>
                        <p class="card-text text-muted">${reminder.description || 'No description'}</p>
                        <div class="d-flex gap-2">
                            <span class="badge bg-info">${formatDateTime(reminder.reminder_time)}</span>
                            <span class="badge bg-${getStatusColor(reminder.status)}">${reminder.status}</span>
                            ${reminder.is_recurring ? `<span class="badge bg-warning">${reminder.recurrence_pattern}</span>` : ''}
                            ${reminder.task_title ? `<span class="badge bg-secondary">Task: ${reminder.task_title}</span>` : ''}
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <small class="text-muted">
                            User: ${reminder.username || 'Unknown'}<br>
                            Created: ${formatDate(reminder.created_at)}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// Save user
async function saveUser() {
    const userData = {
        username: document.getElementById('userUsername').value,
        email: document.getElementById('userEmail').value,
        full_name: document.getElementById('userFullName').value,
        password: document.getElementById('userPassword').value,
        role: document.getElementById('userRole').value,
        status: document.getElementById('userStatus').value
    };
    
    try {
        const response = await fetch(`${API_BASE}/admin/users`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(userData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('User created successfully!', 'success');
            document.getElementById('userForm').reset();
            bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();
            loadAdminDashboardData();
            if (document.getElementById('users-section').style.display !== 'none') {
                loadUsers();
            }
        } else {
            showAlert(data.error || 'Failed to create user', 'danger');
        }
    } catch (error) {
        console.error('Error saving user:', error);
        showAlert('Error saving user', 'danger');
    }
}

// Utility functions
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString();
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString();
}

function getPriorityColor(priority) {
    const colors = {
        'high': 'danger',
        'medium': 'warning',
        'low': 'success'
    };
    return colors[priority] || 'secondary';
}

function getStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'in_progress': 'info',
        'completed': 'success',
        'active': 'info',
        'cancelled': 'secondary'
    };
    return colors[status] || 'secondary';
}

function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container');
    alertContainer.innerHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Auto-hide success alerts
    if (type === 'success') {
        setTimeout(() => {
            const alert = alertContainer.querySelector('.alert');
            if (alert) {
                bootstrap.Alert.getInstance(alert).close();
            }
        }, 3000);
    }
}

// Search and filter functions
function searchUsers() {
    // Implement user search
    loadUsers();
}

function filterActivities() {
    loadActivities();
}

function searchActivities() {
    loadActivities();
}

// User management functions (placeholders)
function editUser(userId) {
    console.log('Edit user:', userId);
    // Implement edit functionality
}

function viewUserDetails(userId) {
    console.log('View user details:', userId);
    // Implement view details functionality
}

async function deleteUser(userId) {
    if (!confirm('Are you sure you want to delete this user?')) {
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE}/admin/users/${userId}`, {
            method: 'DELETE'
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('User deleted successfully!', 'success');
            loadAdminDashboardData();
            if (document.getElementById('users-section').style.display !== 'none') {
                loadUsers();
            }
        } else {
            showAlert(data.error || 'Failed to delete user', 'danger');
        }
    } catch (error) {
        console.error('Error deleting user:', error);
        showAlert('Error deleting user', 'danger');
    }
}

// Logout
async function logout() {
    try {
        await fetch(`${API_BASE}/auth/logout`, {
            method: 'POST'
        });
    } catch (error) {
        console.error('Logout error:', error);
    } finally {
        localStorage.removeItem('user');
        window.location.href = 'login.html';
    }
}
