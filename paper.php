<?php include("bootfile.php"); 
	$user_ob->user_page();
	$paper_class = CLASS_PATH.'Paper.Class.php';	require_once((string)$paper_class);	unset($paper_class);
	$paper_ob = new Paper();
		
	if(!empty($_POST) and isset($_POST['id'])){
		$paper_ob->update_paper($_POST);
		header('location:paper.php');
	}
	else if(!empty($_POST)){
		$paper_ob->add_paper($_POST);
		header('location:paper.php');
	}
	else{ }
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="delete"){
		$paper_ob->delete_paper($_REQUEST['id']);
		header('location:paper.php');
	}
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){
		$row = $paper_ob->select_paper_id($_REQUEST['id']);
		$_POST=$row[0];
	} 
?>
<?php include(INC_PATH."header.php"); ?>
<div class="container mt-2">
  <h3>Paper</h3>
  <p>Paper name is very important it is display in test section.</p>
  <form action="#" method="POST">
	<div class="row m-1"> 
		<div class="col">
			<label for="Paper Name">Paper Name :</label>
			<input type="text" class="form-control" placeholder="Enter Paper Name" name="name" value="<?php if(!empty($_POST['name'])){ echo $_POST['name']; } ?>" >
		</div>
		<div class="col">
			<label for="Exam time">Time :</label>
			<input type="number" class="form-control" placeholder="Enter Exam time" name="time" value="<?php if(!empty($_POST['time'])){ echo $_POST['time']; } ?>" >
		</div>
		<div class="col">
	  	<label for="Status">Status:</label>	  
	  	<select class="form-select" name="status">		  	
			<option <?php if(!empty($_REQUEST['status']) and $_REQUEST['status'] == $standard_row['status']){ echo "selected"; } ?> >Public</option>
			<option <?php if(!empty($_REQUEST['status']) and $_REQUEST['status'] == $standard_row['status']){ echo "selected"; } ?> >Private</option>
			<option <?php if(!empty($_REQUEST['status']) and $_REQUEST['status'] == $standard_row['status']){ echo "selected"; } ?> >Draft</option>
		</select>
	  </div>
		<?php if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){ ?>
			<input type="hidden" name="id" value="<?=$_POST['id']?>">
		<?php } ?>
		<input type="hidden" name="user_id" value="<?=$_SESSION['user_id']?>">
		<div class="row p-4">
			<button type="submit" class="btn btn-primary btn-block">Submit</button>
		</div>
	</div>
  </form>

  <h2>View All Papers</h2>
  <p>Edit and delete name of paper only, edit questions go to Test section.</p>
  <?php $rows = $paper_ob->view_paper_add_by_user($_SESSION['user_id']); $k=0; ?>  
  <div class="table-responsive">
  <table id="<?=$page_name?>" class="table table-striped">
    <thead>
      <tr>
        <th>No</th>
        <th>Paper Name</th>
		<th>Time</th>
		<th>Status</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
	<?php foreach($rows as $row){ $k++; ?>
      <tr>
        <td><?=$k?></td>
        <td><?=$row["name"]?></td>
		<td><?=$row["time"]?></td>
		<td><?=$row["status"]?></td>
        <td><a href="?action=edit&id=<?=$row['id']?>"><i class="fa fa-edit" title="Edit Paper"></i></a>
			<a href="paperset.php?paperid=<?= $row['id'] ?>"><i class="fa fa-paste" title="Edit in Question"></i></a>
			<a href="?action=delete&id=<?=$row['id']?>"><i class="fa fa-times" style="color:red" title="Delete Paper"></i></a></td>
      </tr>
	<?php } ?>
    </tbody>
  </table>
  </div>
</div>
<?php include(INC_PATH."footer.php"); ?>