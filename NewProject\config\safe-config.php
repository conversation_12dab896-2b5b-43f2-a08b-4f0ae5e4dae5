<?php
/**
 * Safe Configuration
 * Minimal config without problematic includes
 */

// Start session safely
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Define constants
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__) . '/');
}
if (!defined('CLASSES_PATH')) {
    define('CLASSES_PATH', BASE_PATH . 'classes/');
}

// Helper functions
if (!function_exists('validateInput')) {
    function validateInput($data) {
        return htmlspecialchars(strip_tags(trim($data)));
    }
}

if (!function_exists('isLoggedIn')) {
    function isLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
}

if (!function_exists('isAdmin')) {
    function isAdmin() {
        return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
    }
}

// Safe class loading function
function safeLoadClass($className) {
    $file = CLASSES_PATH . $className . '.php';
    if (file_exists($file)) {
        require_once $file;
        return class_exists($className);
    }
    return false;
}

// Load Database class safely
if (!class_exists('Database')) {
    $db_file = BASE_PATH . 'config/database.php';
    if (file_exists($db_file)) {
        require_once $db_file;
    }
}
?>
