<?php include("bootfile.php"); 
	$user_ob->user_page();
	$standard_class = CLASS_PATH.'Standard.Class.php';	require_once((string)$standard_class);	unset($standard_class);
	$standard_ob = new Standard();
	$subject_class = CLASS_PATH.'Subject.Class.php';	require_once((string)$subject_class);	unset($subject_class);
	$subject_ob = new Subject();
	$paper_class = CLASS_PATH.'Paper.Class.php';	require_once((string)$paper_class);	unset($paper_class);
	$paper_ob = new Paper();
	$papersection_class = CLASS_PATH.'PaperSection.Class.php';	require_once((string)$papersection_class);	unset($papersection_class);
	$papersection_ob = new PaperSection();
	$question_class = CLASS_PATH.'Question.Class.php';	require_once((string)$question_class);	unset($question_class);
	$question_ob = new Question();
	$paperset_class = CLASS_PATH.'PaperSet.Class.php';	require_once((string)$paperset_class);	unset($paperset_class);
	$paperset_ob = new PaperSet();
	
	if(!empty($_POST) and isset($_POST['id'])){		
		$paperset_ob->update_paperset($_POST);
		header('location:paperset.php?paperid='.$_POST['paper_id']);
	}
	else if(!empty($_POST)){		
		$paperset_ob->add_in_paperset($_POST);
		header('location:paperset.php?paperid='.$_POST['paper_id'].'&papersectionid='.$_POST['papersection_id'].'&standardid='.$_POST['standard_id'].'&subjectid='.$_POST['subject_id']);
	}
	else{ }
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="delete"){
		$array = $paperset_ob->select_paperset_id($_REQUEST['id']);
		$paperset_ob->delete_paperset($_REQUEST['id']);
		$row=$array[0];
		header('location:paperset.php?paperid='.$row['paper_id'].'&papersectionid='.$row['section_id'].'&standardid='.$row['standard_id'].'&subjectid='.$row['subject_id']);
	}
	if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){
		$row = $paperset_ob->select_paperset_question($_REQUEST['id']);
		$_POST=$row[0]; 
	} 
?>
<?php include(INC_PATH."header.php"); ?>
<style>
.cke_editable {
	margin: 2 !important;
}
</style>
<div class="container mt-2">
  <h3>Set Question Paper </h3>
  <p>Must checkor select Exam(Standard), paper name, subject and section carefully. If not add then Add first</p>
  <form action="paperset.php" method="POST">
  	<div class="row">
	  <div class="col">
	  <label for="Standard">Select Standard or Exam:</label>
	  <?php $standard_rows = $standard_ob->view_standard_add_by_user($_SESSION['user_id']);; ?>
	  	<select class="form-select form-select-lg" name="standard_id" required>
		  	<?php foreach($standard_rows as $standard_row){ ?>  
				<option value="<?=$standard_row['id']?>" <?php if(!empty($_REQUEST['standardid']) and $_REQUEST['standardid'] == $standard_row['id']){ echo "selected"; } ?> <?php if(!empty($_POST['standard_id']) and $_POST['standard_id'] == $standard_row['id']){ echo "selected"; } ?> ><?=$standard_row['name']?></option>
			<?php } unset($standard_row); ?>	
		</select>
	  </div>
	  <div class="col">
	  <label for="Subject">Select Subject:</label>
	  <?php $subject_rows = $subject_ob->view_subject_add_by_user($_SESSION['user_id']); ?> 
	  	<select class="form-select form-select-lg" name="subject_id" required>
	  		<?php foreach($subject_rows as $subject_row){ ?>
				<option value="<?=$subject_row['id']?>" <?php if(!empty($_REQUEST['subjectid']) and $_REQUEST['subjectid'] == $subject_row['id']){ echo "selected"; } ?> <?php if(!empty($_POST['subject_id']) and $_POST['subject_id'] == $subject_row['id']){ echo "selected"; } ?> ><?=$subject_row['name']?></option>
			<?php } unset($rows_papersection); ?>
		</select>
	  </div>
	</div>
	<div class="row mt-3">
	  <div class="col">
	  <label for="Paper">Select Paper:</label>	  
	  <?php $paper_rows = $paper_ob->view_paper_add_by_user($_SESSION['user_id']); ?>
	  	<select class="form-select form-select-lg" name="paper_id" required>
		  	<?php foreach($paper_rows as $paper_row){ ?>  
				<option value="<?=$paper_row['id']?>" <?php if(!empty($_REQUEST['paperid']) and $_REQUEST['paperid'] == $paper_row['id']){ echo "selected"; } ?> <?php if(!empty($_POST['paper_id']) and $_POST['paper_id'] == $paper_row['id']){ echo "selected"; } ?> ><?=$paper_row['name']?></option>
			<?php } unset($paper_rows); ?>	
		</select>
	  </div>
	  <div class="col">
	  <label for="Section">Select Section:</label>	  
	  <?php $rows_papersection = $papersection_ob->view_papersection_add_by_user($_SESSION['user_id']); ?> 
	  	<select class="form-select form-select-lg" name="papersection_id">
	  		<?php foreach($rows_papersection as $row_papersection){ ?>
				<option value="<?=$row_papersection['id']?>" <?php if(!empty($_REQUEST['papersectionid']) and $_REQUEST['papersectionid'] == $row_papersection['id']){ echo "selected"; } ?> <?php if(!empty($_POST['section_id']) and $_POST['section_id'] == $row_papersection['id']){ echo "selected"; } ?> ><?=$row_papersection['name']?></option>
			<?php } unset($rows_papersection); ?>
		</select>
	  </div>
	</div>
	<div class="m-1">
		<label for="que">Question</label>
		<textarea id="que" class="form-control" name="que"><?php if(!empty($_POST['que'])){ echo $_POST['que']; } ?></textarea>			
	</div>
	<div class="m-1">
		<label for="Opt1">Option 1</label>
		<textarea id="opt1" class="form-control" rows="2" name="opt1"><?php if(!empty($_POST['opt1'])){ echo $_POST['opt1']; } ?></textarea>
	</div>
	<div class="m-1">
		<label for="opt2">Option 2</label>
		<textarea class="form-control" rows="2" id="opt2" name="opt2"><?php if(!empty($_POST['opt2'])){ echo $_POST['opt2']; } ?></textarea>
	</div>
	<div class="m-1">
		<label for="opt3">Option 3</label>
		<textarea class="form-control" rows="2" id="opt3" name="opt3"><?php if(!empty($_POST['opt3'])){ echo $_POST['opt3']; } ?></textarea>
	</div>
	<div class="m-1">
		<label for="opt4">Option 4</label>
		<textarea class="form-control" rows="2" id="opt4" name="opt4"><?php if(!empty($_POST['opt4'])){ echo $_POST['opt4']; } ?></textarea>
	</div>
	<div class="m-1">
		<label for="opt5">Option 5</label>
		<textarea class="form-control" rows="2" id="opt5" name="opt5"><?php if(!empty($_POST['opt5'])){ echo $_POST['opt5']; } ?></textarea>
	</div>
	<div class="m-1">
		<label for="ans">Answer</label>
		<textarea class="form-control" rows="2" id="ans" name="ans"><?php if(!empty($_POST['ans'])){ echo $_POST['ans']; } ?></textarea>
	</div>
	<input type="hidden" name="user_id" value="<?=$_SESSION['user_id']?>">
	<?php if(!empty($_REQUEST['action']) and $_REQUEST['action']=="edit"){ ?>
		<input type="hidden" name="id" value="<?=$_POST['id']?>">
		<input type="hidden" name="question_id" value="<?=$_POST['question_id']?>">
	<?php } ?>	

	<div class="row p-3">
		<button type="submit" class="btn btn-primary btn-block">Submit</button>
	</div>
  </form>

  <h2>View All Questions</h2>
  <p>Must check answer it must be same as option.</p>
  <div class="table-responsive">
  <table class="table table-striped">
    <thead>
      <tr>
        <th>No</th> 		<th>Question</th>
		<th>Option 1</th>	<th>Option 2</th>
		<th>Option 3</th>	<th>Option 4</th>
		<th>Option 5</th>	<th>Answer</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
	<?php $paper_id=0; if(isset($_REQUEST['paperid'])){ $paper_id=$_REQUEST['paperid']; } ?>
  	<?php $rows = $paperset_ob->select_paperset_all_questions($paper_id); $k=0; ?>
	<?php foreach($rows as $row){ $k++; ?>
      <tr>
        <td><?=$k?></td>
        <td><?=$row["que"]?></td>
		<td><?=$row["opt1"]?></td>
		<td><?=$row["opt2"]?></td>
		<td><?=$row["opt3"]?></td>
		<td><?=$row["opt4"]?></td>
		<td><?=$row["opt5"]?></td>
		<td><?=$row["ans"]?></td>
        <td><a href="?action=edit&id=<?=$row['id']?>"><i class="fa fa-edit"></i></a>
			<a href="?action=delete&id=<?=$row['id']?>"><i class="fa fa-times" style="color:red"></i></a></td>
      </tr>
	<?php } ?>
    </tbody>
  </table>
  </div>
</div>
<script src="https://cdn.ckeditor.com/4.18.0/standard-all/ckeditor.js"></script>
<script>
  CKEDITOR.replace('que', { extraPlugins: 'mathjax', mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.4/MathJax.js?config=TeX-AMS_HTML', removeButtons: 'PasteFromWord', height:'150' });
  CKEDITOR.replace('opt1', { extraPlugins: 'mathjax', mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.4/MathJax.js?config=TeX-AMS_HTML', removeButtons: 'PasteFromWord' });
  CKEDITOR.replace('opt2', { extraPlugins: 'mathjax', mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.4/MathJax.js?config=TeX-AMS_HTML', removeButtons: 'PasteFromWord' });
  CKEDITOR.replace('opt3', { extraPlugins: 'mathjax', mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.4/MathJax.js?config=TeX-AMS_HTML', removeButtons: 'PasteFromWord' });
  CKEDITOR.replace('opt4', { extraPlugins: 'mathjax', mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.4/MathJax.js?config=TeX-AMS_HTML', removeButtons: 'PasteFromWord' });
  CKEDITOR.replace('opt5', { extraPlugins: 'mathjax', mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.4/MathJax.js?config=TeX-AMS_HTML', removeButtons: 'PasteFromWord' });
  CKEDITOR.replace('ans', { extraPlugins: 'mathjax', mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.4/MathJax.js?config=TeX-AMS_HTML', removeButtons: 'PasteFromWord' });
  CKEDITOR.config.height=80; 
  if (CKEDITOR.env.ie && CKEDITOR.env.version == 8) { document.getElementById('ie8-warning').className = 'tip alert'; }
  
</script>
<style>
	.cke_editable{
		margin: 2;
	}
.cke_top,.cke_bottom {
  display: none;
} </style>
<?php include(INC_PATH."footer.php"); ?>