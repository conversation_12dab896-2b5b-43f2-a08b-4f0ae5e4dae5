<?php include("bootfile.php"); 
  	$user_ob->user_page();
  	$question_class = CLASS_PATH.'Question.Class.php';	require_once((string)$question_class);	unset($question_class);
	$question_ob = new Question();
	$paperset_class = CLASS_PATH.'PaperSet.Class.php';	require_once((string)$paperset_class);	unset($paperset_class);
	$paperset_ob = new PaperSet();
  	
	$result_class = CLASS_PATH.'Result.Class.php';	require_once((string)$result_class);	unset($result_class);
	$result_ob = new Result();
    $_POST = $result_ob->select_submited_answer_of_paper($_REQUEST['attemptid']);
  	
	$paper_id=0; if(isset($_REQUEST['paperid'])){ $paper_id=$_REQUEST['paperid']; } 
  	$rows = $paperset_ob->select_paperset_all_questions($paper_id); 
  	$k=0; $p=0; ?>
<?php include(INC_PATH."header.php"); ?>
<style>  p{ margin: 0;  } </style>
<div class="container mt-3">
<h2><?php if(isset($rows[0]['name'])){ echo$rows[0]['name']; } else { header('location:index.php'); } ?> </h2>	
<?php foreach($rows as $row){ $p++; $row["id"]; ?>
<div class="card"> 
  <div class="card-body">
	<h4 class="card-title">
		<?php if($_POST[$k]['question_id']==$row["question_id"]){ $k++; }
		if(!empty($_POST[$k-1]['answer']) and $_POST[$k-1]['answer'] == $row["ans"]){ ?>
			<i class="fa fa-check" style="font-size:24px;color:green"></i>
		<?php $right_ans++; } else { ?>
			<i class="fa fa-times" style="font-size:24px;color:red"></i> 
		<?php } ?> 
		Question: <?=$p?>  </h4>
		<p class="card-text"> 
		<?=$row["que"]?> </p>
		<p class="pb-3"></p>
		<?php if($row["opt1"]!=""){ ?> <?php if($_POST[$k]['question_id']==$row["question_id"]){ $k++; } ?>
		<div class="list-group-item">
		<label class="form-check <?php if(!empty($_POST[$k-1]['answer']) and $_POST[$k-1]['answer'] == $row["opt1"]){echo 'fw-bold'; } if($row["opt1"]==$row["ans"]){ echo ' text-success fw-bold'; } ?>"><input class="form-check-input" type="radio" disabled <?php if(!empty($_POST[$k-1]['answer']) and $_POST[$k-1]['answer'] == $row["opt1"]){ echo 'checked'; } ?> /> <?=$row["opt1"]?></label>
		</div>
		<?php } if($row["opt2"]!=""){ ?>
		<div class="list-group-item">
		<label class="form-check <?php if(!empty($_POST[$k-1]['answer']) and $_POST[$k-1]['answer'] == $row["opt2"]){echo 'fw-bold'; } if($row["opt2"]==$row["ans"]){ echo ' text-success fw-bold'; } ?>"><input class="form-check-input" type="radio" disabled <?php if(!empty($_POST[$k-1]['answer']) and $_POST[$k-1]['answer'] == $row["opt2"]){ echo 'checked'; } ?> /> <?=$row["opt2"]?></label>
		</div>
		<?php } if($row["opt3"]!=""){ ?>
		<div class="list-group-item">
		<label class="form-check <?php if(!empty($_POST[$k-1]['answer']) and $_POST[$k-1]['answer'] == $row["opt3"]){echo 'fw-bold'; } if($row["opt3"]==$row["ans"]){ echo ' text-success fw-bold'; } ?>"><input class="form-check-input" type="radio" disabled <?php if(!empty($_POST[$k-1]['answer']) and $_POST[$k-1]['answer'] == $row["opt3"]){ echo 'checked'; } ?> /> <?=$row["opt3"]?></label>
		</div>
		<?php } if($row["opt4"]!=""){ ?>
		<div class="list-group-item">
		<label class="form-check <?php if(!empty($_POST[$k-1]['answer']) and $_POST[$k-1]['answer'] == $row["opt4"]){echo 'fw-bold'; } if($row["opt4"]==$row["ans"]){ echo ' text-success fw-bold'; } ?>"><input class="form-check-input" type="radio" disabled <?php if(!empty($_POST[$k-1]['answer']) and $_POST[$k-1]['answer'] == $row["opt4"]){ echo 'checked'; } ?> /> <?=$row["opt4"]?></label>
		</div>
		<?php } if($row["opt5"]!=""){ ?>
		<div class="list-group-item">
		<label class="form-check <?php if(!empty($_POST[$k-1]['answer']) and $_POST[$k-1]['answer'] == $row["opt5"]){echo 'fw-bold'; }  if($row["opt5"]==$row["ans"]){ echo ' text-success fw-bold'; } ?>"><input class="form-check-input" type="radio" disabled <?php if(!empty($_POST[$k-1]['answer']) and $_POST[$k-1]['answer'] == $row["opt5"]){ echo 'checked'; } ?> /> <?=$row["opt5"]?></label>
		</div>
		<?php } ?>
	</div>
  </div>
  <?php } ?>
  <!-- The Modal -->
  <div class="modal show" id="resultpopup">
	<div class="modal-dialog">
		<div class="modal-content">
		<!-- Modal Header -->
		<div class="modal-header">
			<h4 class="modal-title">Check Result and Answer</h4>
			<button type="button" class="btn-close" data-bs-dismiss="modal"></button>
		</div>
		<!-- Modal body -->
		<div class="modal-body">
			Attended Questions : <label id="Total_attend_que"><?=$result_ob->select_attend_question_of_paper($paper_id,$_REQUEST['attemptid'])?></label><br/>
			Right Answer       : <label id="Not_attend_que"><?=$result_ob->select_right_answer_of_paper($paper_id,$_REQUEST['attemptid'])?></label><br/>
		</div>
		<!-- Modal footer -->
		<div class="modal-footer">
			<button type="button" class="btn btn-info" data-bs-dismiss="modal">OK</button>
		</div>
		</div>
	</div>
  </div>
  <?php if(substr(basename($_SERVER['HTTP_REFERER']),0,4)=="exam"){ ?> 
	<script>
   		var myModal = new bootstrap.Modal(document.getElementById("resultpopup"), {});
		document.onreadystatechange = function () {
  		myModal.show(); };
  	</script>	
  <?php }?>
</div>
<?php  include("part/footer.php"); ?>