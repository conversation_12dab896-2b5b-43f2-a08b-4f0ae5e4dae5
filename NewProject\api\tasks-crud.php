<?php
/**
 * Tasks CRUD Endpoint
 * Simple tasks management without complex includes
 */

// Start session
session_start();

// Set headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=UTF-8');

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit();
}

// Database configuration
$host = 'localhost';
$db_name = 'task_reminder_app';
$username = 'root';
$password = '';

$method = $_SERVER['REQUEST_METHOD'];
$user_id = $_SESSION['user_id'];

try {
    // Create database connection
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    switch ($method) {
        case 'GET':
            // Get all tasks for user
            $stmt = $pdo->prepare("
                SELECT id, title, description, priority, status, due_date, created_at, updated_at
                FROM tasks 
                WHERE user_id = ? 
                ORDER BY 
                    CASE priority 
                        WHEN 'high' THEN 1 
                        WHEN 'medium' THEN 2 
                        WHEN 'low' THEN 3 
                    END,
                    due_date ASC
            ");
            $stmt->execute([$user_id]);
            $tasks = $stmt->fetchAll();
            
            echo json_encode([
                'success' => true,
                'tasks' => $tasks,
                'count' => count($tasks)
            ]);
            break;
            
        case 'POST':
            // Create new task
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            if (empty($data['title'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Title is required']);
                exit();
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO tasks (user_id, title, description, priority, status, due_date) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $user_id,
                $data['title'],
                $data['description'] ?? '',
                $data['priority'] ?? 'medium',
                $data['status'] ?? 'pending',
                $data['due_date'] ?? null
            ]);
            
            $task_id = $pdo->lastInsertId();
            
            // Log activity
            $log_stmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address) 
                VALUES (?, 'create', 'task', ?, ?, ?)
            ");
            $log_stmt->execute([
                $user_id,
                $task_id,
                json_encode(['title' => $data['title']]),
                $_SERVER['REMOTE_ADDR'] ?? ''
            ]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Task created successfully',
                'task_id' => $task_id
            ]);
            break;
            
        case 'PUT':
            // Update task
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            if (empty($data['id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Task ID is required']);
                exit();
            }
            
            // Check if task belongs to user
            $check_stmt = $pdo->prepare("SELECT id FROM tasks WHERE id = ? AND user_id = ?");
            $check_stmt->execute([$data['id'], $user_id]);
            
            if (!$check_stmt->fetch()) {
                http_response_code(404);
                echo json_encode(['error' => 'Task not found']);
                exit();
            }
            
            $stmt = $pdo->prepare("
                UPDATE tasks 
                SET title = ?, description = ?, priority = ?, status = ?, due_date = ?
                WHERE id = ? AND user_id = ?
            ");
            
            $stmt->execute([
                $data['title'],
                $data['description'] ?? '',
                $data['priority'] ?? 'medium',
                $data['status'] ?? 'pending',
                $data['due_date'] ?? null,
                $data['id'],
                $user_id
            ]);
            
            // Log activity
            $log_stmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address) 
                VALUES (?, 'update', 'task', ?, ?, ?)
            ");
            $log_stmt->execute([
                $user_id,
                $data['id'],
                json_encode(['title' => $data['title'], 'status' => $data['status']]),
                $_SERVER['REMOTE_ADDR'] ?? ''
            ]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Task updated successfully'
            ]);
            break;
            
        case 'DELETE':
            // Delete task
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            if (empty($data['id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Task ID is required']);
                exit();
            }
            
            // Check if task belongs to user and get title for logging
            $check_stmt = $pdo->prepare("SELECT title FROM tasks WHERE id = ? AND user_id = ?");
            $check_stmt->execute([$data['id'], $user_id]);
            $task = $check_stmt->fetch();
            
            if (!$task) {
                http_response_code(404);
                echo json_encode(['error' => 'Task not found']);
                exit();
            }
            
            $stmt = $pdo->prepare("DELETE FROM tasks WHERE id = ? AND user_id = ?");
            $stmt->execute([$data['id'], $user_id]);
            
            // Log activity
            $log_stmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address) 
                VALUES (?, 'delete', 'task', ?, ?, ?)
            ");
            $log_stmt->execute([
                $user_id,
                $data['id'],
                json_encode(['title' => $task['title']]),
                $_SERVER['REMOTE_ADDR'] ?? ''
            ]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Task deleted successfully'
            ]);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Database error',
        'message' => $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error',
        'message' => $e->getMessage()
    ]);
}
?>
