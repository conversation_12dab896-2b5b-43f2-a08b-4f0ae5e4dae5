<?php
class PaperSet extends Question{
	
	public $table_paperset = "paperset";
	// paperset file functions 
	public function add_in_paperset($post){
		$paperset['paper_id'] = $post['paper_id']; unset($post['paper_id']);
		$paperset['section_id'] = $post['papersection_id']; unset($post['papersection_id']);
		$paperset['subject_id'] = $post['subject_id']; unset($post['subject_id']);
		$paperset['standard_id'] = $post['standard_id']; unset($post['standard_id']);
		parent::add_question($post);		
		$paperset['question_id'] = $this->last_inser_id; 
		$paperset['user_id'] = $_SESSION['user_id'];

		$query = parent::insert_record($this->table_paperset,$paperset);
		$data = parent::update_query($query); 
	}	
	public function update_paperset($post){
		$paperset['standard_id'] = $post['standard_id']; unset($post['standard_id']);
		$paperset['subject_id'] = $post['subject_id']; unset($post['subject_id']);
		$paperset['paper_id'] = $post['paper_id']; unset($post['paper_id']);
		$paperset['user_id'] = $post['user_id']; unset($post['user_id']);
		$paperset['section_id'] = $post['papersection_id']; unset($post['papersection_id']);
		$paperset['id'] = $post['id']; unset($post['id']);
		$paperset['question_id'] = $post['question_id']; unset($post['question_id']);
		$post['id']=$paperset['question_id'];
		parent::update_question($post);
		$query = parent::update_record($this->table_paperset,$paperset,$paperset['id']);
		$data = parent::update_query($query);
	}
	//this function also used in exam.php and result.php
	public function select_paperset_all_questions($paperid){
		$query = "SELECT * FROM paper JOIN question JOIN paperset
			ON paperset.paper_id=paper.id AND paperset.question_id=question.id 
			WHERE paper.id=".$paperid;
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function select_paperset_question($papersetid){
		$query = "SELECT * FROM paper JOIN question JOIN paperset
			ON paperset.paper_id=paper.id AND paperset.question_id=question.id 
			WHERE paperset.id=".$papersetid;
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function delete_paperset($id){
		$array = $this->select_paperset_id($id);
		$row=$array[0];
		$query = parent::delete_record($this->table_paperset,$id);
		$result = parent::update_query($query);
		parent::delete_question($row['question_id']);
	}
	// testlist.php to count paper total questions.
	public function select_paperset_no_of_question($paperid){
		$query = "SELECT COUNT(*) AS `paper_questin` FROM `paperset` WHERE paper_id=".$paperid;
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array[0][0];
	}
	//
	public function get_question_id($id){
		$query = parent::select_field("question_id",$this->table_paperset,$id);		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array[0][0];
	}
	public function select_paperset_id($id){
		$query = parent::select_record($this->table_paperset,$id);		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}	
}
?>