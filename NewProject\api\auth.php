<?php
/**
 * Authentication API Endpoints
 * Based on PreTest.app authentication but RESTful
 */

require_once '../config/config.php';

$method = $_SERVER['REQUEST_METHOD'];
$request_uri = $_SERVER['REQUEST_URI'];
$path_parts = explode('/', trim(parse_url($request_uri, PHP_URL_PATH), '/'));

// Get the action from URL
$action = isset($path_parts[2]) ? $path_parts[2] : '';

switch ($method) {
    case 'POST':
        if ($action === 'login') {
            login();
        } elseif ($action === 'register') {
            register();
        } elseif ($action === 'logout') {
            logout();
        } else {
            jsonResponse(['error' => 'Invalid endpoint'], 404);
        }
        break;
    
    case 'GET':
        if ($action === 'profile') {
            getProfile();
        } elseif ($action === 'check') {
            checkAuth();
        } else {
            jsonResponse(['error' => 'Invalid endpoint'], 404);
        }
        break;
    
    default:
        jsonResponse(['error' => 'Method not allowed'], 405);
        break;
}

function login() {
    $data = json_decode(file_get_contents("php://input"), true);
    
    if (empty($data['username']) || empty($data['password'])) {
        jsonResponse(['error' => 'Username and password are required'], 400);
    }
    
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);
    
    if ($user->login($data['username'], $data['password'])) {
        jsonResponse([
            'success' => true,
            'message' => 'Login successful',
            'user' => [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'role' => $_SESSION['user_role'],
                'full_name' => $_SESSION['full_name']
            ]
        ]);
    } else {
        jsonResponse(['error' => 'Invalid credentials'], 401);
    }
}

function register() {
    $data = json_decode(file_get_contents("php://input"), true);
    
    // Validate required fields
    $required_fields = ['username', 'email', 'password', 'full_name'];
    foreach ($required_fields as $field) {
        if (empty($data[$field])) {
            jsonResponse(['error' => ucfirst($field) . ' is required'], 400);
        }
    }
    
    // Validate email format
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        jsonResponse(['error' => 'Invalid email format'], 400);
    }
    
    // Validate password strength
    if (strlen($data['password']) < 6) {
        jsonResponse(['error' => 'Password must be at least 6 characters long'], 400);
    }
    
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);
    
    // Set user properties
    $user->username = $data['username'];
    $user->email = $data['email'];
    $user->password = $data['password'];
    $user->full_name = $data['full_name'];
    $user->role = 'user'; // Default role
    $user->status = 'active';
    
    // Check if user already exists
    if ($user->userExists()) {
        jsonResponse(['error' => 'Username or email already exists'], 409);
    }
    
    if ($user->create()) {
        jsonResponse([
            'success' => true,
            'message' => 'User registered successfully',
            'user_id' => $user->id
        ], 201);
    } else {
        jsonResponse(['error' => 'Failed to register user'], 500);
    }
}

function logout() {
    requireAuth();
    
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);
    
    if ($user->logout()) {
        jsonResponse([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    } else {
        jsonResponse(['error' => 'Failed to logout'], 500);
    }
}

function getProfile() {
    requireAuth();
    
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);
    $user->id = $_SESSION['user_id'];
    
    if ($user->readOne()) {
        jsonResponse([
            'success' => true,
            'user' => [
                'id' => $user->id,
                'username' => $user->username,
                'email' => $user->email,
                'full_name' => $user->full_name,
                'role' => $user->role,
                'status' => $user->status,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at
            ]
        ]);
    } else {
        jsonResponse(['error' => 'User not found'], 404);
    }
}

function checkAuth() {
    if (isLoggedIn()) {
        jsonResponse([
            'authenticated' => true,
            'user' => [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'role' => $_SESSION['user_role'],
                'full_name' => $_SESSION['full_name']
            ]
        ]);
    } else {
        jsonResponse([
            'authenticated' => false
        ]);
    }
}
?>
