<!DOCTYPE html>
<html lang="en">

<head>
  <title>PreTest, Test befor Exam </title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
    <?php $page_name  = rtrim(basename($_SERVER['REQUEST_URI'], '?' . $_SERVER['QUERY_STRING']),".php");
    if($page_name == "resultlist2.php" or "resultlist.php" or "paper.php" or "question.php"){ ?>
    <link href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script> $(document).ready(function() { $('#<?=$page_name?>').DataTable(); } ); </script>  
  <?php } ?>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">  
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
  <link rel="icon" type="image/png" href="images.jpg">
  <link rel="manifest" href="manifest.json">
  
</head>

<body>
  <nav class="navbar sticky-top navbar-expand-md bg-light navbar-light">
    <div class="container-fluid">
      <a class="navbar-brand" href="index.php">
        <div><span class="h1" style="color:orange">Pre</span><span class="h1" style="color:black">Test</span>
        </div>
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#collapsibleNavbar">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse justify-content-end" id="collapsibleNavbar">
        <ul class="navbar-nav justify-content-end">
          <li class="nav-item"><a class="nav-link" href="testlist.php">Test</a></li>
          <?php if ($user_ob->foruser()) { ?>
            <li class="nav-item"><a class="nav-link" href="resultlist.php">Result</a></li>
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">User</a>
              <ul class="dropdown-menu">
                <?php if($_SESSION['user_id']!="2"){ ?>
                  <li><a class="dropdown-item" href="userprofile.php">Profile</a></li>
                <?php } ?>
                <li><a class="dropdown-item" href="signin.php?signout=yes">SignOut</a></li>
                <?php if ($_SESSION['user_type']=="Boss") { ?>
                  <li><a class="dropdown-item" href="user.php">User Maser</a></li>
                <?php } ?>
              </ul>
            </li>
          <?php } else { ?>
            <li class="nav-item"><a class="nav-link" href="signin.php">SignIn</a></li>
          <?php } ?>
          <?php if ($user_ob->forauthor() or $user_ob->foradmin()) { ?>
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">Exam</a>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="paperset.php">Set Paper</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="standard.php">Exam(Standard)</a></li>
                <li><a class="dropdown-item" href="subject.php">Subject</a></li>
                <li><a class="dropdown-item" href="paper.php">Paper</a></li>
                <li><a class="dropdown-item" href="papersection.php">Section</a></li>
                <?php if ($user_ob->foradmin()) { ?>
                  <li><hr class="dropdown-divider"></li>
                  <li><a class="dropdown-item" href="question.php">Question</a></li>
                <?php } ?>
              </ul>
            </li>
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">Supervisor</a>
              <ul class="dropdown-menu">
                <?php if ($user_ob->foradmin()) { ?>
                <li><a class="dropdown-item" href="teacher.php">Teacher List</a></li>
                <?php } ?>
                <li><a class="dropdown-item" href="student.php">Students List</a></li>
                <li><a class="dropdown-item" href="resultlist2.php">Result List</a></li>
              </ul>
            </li>
          <?php } ?>
          <li class="nav-item"><a class="nav-link" href="contact.php">Contact</a></li>
        </ul>
      </div>
    </div>
  </nav>
  <?php if(!empty($_SESSION['message'])){ ?>
  <div class="m-3 alert alert-success alert-dismissible">
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    <strong>Message : </strong> <?=$_SESSION['message']?> .
    <?php unset($_SESSION["message"]); ?>
  </div>
  <?php } ?>