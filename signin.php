<?php include("bootfile.php");
	if(!empty($_REQUEST['guest']) and $_REQUEST['guest']=='yes'){		
		$user_ob->login('1234567890','1234567890'); }
	if(!empty($_POST)){			
		$user_ob->login($_POST['mobile_no'],$_POST['password']); } 
	if(!empty($_REQUEST['signout']) and $_REQUEST['signout']=='yes'){		
		$user_ob->logout();
		header('location:signin.php');	}
	//if($user_ob->foruser()){ $user_ob->logout(); }
?>
<?php include(INC_PATH."header.php"); ?>
<div class="container mt-2">
  <h3>Sign In</h3>
  <p>Enter Mobile no and password carefully.</p>
  <form action="#" method="POST">
    <div class="row m-1">
		<div class="col">
			<label for="mobile">Mobile No</label>
			<input type="number" class="form-control" placeholder="Enter Mobile no" name="mobile_no" required min="6276957193" max="9999999999" />
		</div>
	</div>
    <div class="row m-1">
		<div class="col">
			<label for="pwd">Password:</label>
			<input type="password" class="form-control" placeholder="Enter password" name="password" required="required" minlength="4" maxlength="20" />
		</div>
    </div>
	<div class="row m-4">
		<button type="submit" class="btn btn-primary btn-block">Submit</button>
	</div>
  </form>
  <div class="card">
  <div class="card-body">
    <h4 class="card-title">Guest User</h4>
    <p class="card-text">Used following Guest Button to SignIn. But guest user data does't save for result and progress tracking. Please Create Account</p>
	<a href="?guest=yes" class="btn btn-info">Guest Login</a> &nbsp;
	<a href="signup.php" class="btn btn-warning btn-lg">Create Account</a>	
  </div>
</div>
</div>	
<?php include(INC_PATH."footer.php"); ?>