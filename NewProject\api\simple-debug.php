<?php
/**
 * Simple Debug Endpoint
 * Basic test without any includes
 */

// Set headers
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');

// Basic debug info
$debug_info = [
    'status' => 'PHP is working',
    'timestamp' => date('Y-m-d H:i:s'),
    'php_version' => phpversion(),
    'current_directory' => __DIR__,
    'server_info' => [
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown'
    ]
];

// Check file existence
$files_to_check = [
    '../config/config.php',
    '../config/database.php',
    '../classes/User.php',
    '../database/schema.sql'
];

$debug_info['file_check'] = [];
foreach ($files_to_check as $file) {
    $full_path = __DIR__ . '/' . $file;
    $debug_info['file_check'][$file] = [
        'exists' => file_exists($full_path),
        'readable' => is_readable($full_path),
        'full_path' => $full_path
    ];
}

// Test basic database connection (without includes)
$debug_info['database_test'] = [];
try {
    $host = 'localhost';
    $db_name = 'task_reminder_app';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;charset=utf8", $username, $password);
    $debug_info['database_test']['connection'] = 'SUCCESS';
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE '$db_name'");
    $debug_info['database_test']['database_exists'] = $stmt->rowCount() > 0;
    
    if ($debug_info['database_test']['database_exists']) {
        // Connect to specific database
        $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8", $username, $password);
        
        // Check tables
        $tables = ['users', 'tasks', 'reminders', 'activity_logs'];
        $debug_info['database_test']['tables'] = [];
        
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            $debug_info['database_test']['tables'][$table] = $stmt->rowCount() > 0;
        }
        
        // Check if admin user exists
        if ($debug_info['database_test']['tables']['users']) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
            $stmt->execute();
            $debug_info['database_test']['admin_user_exists'] = $stmt->fetchColumn() > 0;
        }
    }
    
} catch (PDOException $e) {
    $debug_info['database_test']['error'] = $e->getMessage();
}

// Output the debug info
echo json_encode($debug_info, JSON_PRETTY_PRINT);
?>
