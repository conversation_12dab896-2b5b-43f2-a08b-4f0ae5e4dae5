<?php
/**
 * Simple Debug Endpoint
 * Basic test without any includes
 */

// Set headers
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');

// Basic debug info
$debug_info = array(
    'status' => 'PHP is working',
    'timestamp' => date('Y-m-d H:i:s'),
    'php_version' => phpversion(),
    'current_directory' => __DIR__
);

// Check file existence
$files_to_check = array(
    '../config/config.php',
    '../config/database.php',
    '../classes/User.php',
    '../database/schema.sql'
);

$debug_info['file_check'] = array();
foreach ($files_to_check as $file) {
    $full_path = __DIR__ . '/' . $file;
    $debug_info['file_check'][$file] = array(
        'exists' => file_exists($full_path),
        'readable' => is_readable($full_path),
        'full_path' => $full_path
    );
}

// Output the debug info first (without database test)
echo json_encode($debug_info);
?>
