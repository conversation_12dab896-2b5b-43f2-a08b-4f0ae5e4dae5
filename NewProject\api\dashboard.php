<?php
/**
 * Dashboard API Endpoints
 * User dashboard data and statistics
 */

require_once '../config/config.php';

$method = $_SERVER['REQUEST_METHOD'];
$request_uri = $_SERVER['REQUEST_URI'];
$path_parts = explode('/', trim(parse_url($request_uri, PHP_URL_PATH), '/'));

// Get the action from URL
$action = isset($path_parts[2]) ? $path_parts[2] : '';

// Require authentication for all dashboard endpoints
requireAuth();

switch ($method) {
    case 'GET':
        if ($action === 'overview') {
            getDashboardOverview();
        } elseif ($action === 'tasks') {
            getDashboardTasks();
        } elseif ($action === 'reminders') {
            getDashboardReminders();
        } elseif ($action === 'statistics') {
            getDashboardStatistics();
        } else {
            getDashboardOverview(); // Default to overview
        }
        break;
    
    default:
        jsonResponse(['error' => 'Method not allowed'], 405);
        break;
}

function getDashboardOverview() {
    $user_id = $_SESSION['user_id'];
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Get task statistics
    $task = new Task($db);
    $task_stats = $task->getStatistics($user_id);
    
    // Get upcoming reminders
    $reminder = new Reminder($db);
    $upcoming_reminders_stmt = $reminder->getUpcoming($user_id, 24);
    $upcoming_reminders = [];
    while ($row = $upcoming_reminders_stmt->fetch(PDO::FETCH_ASSOC)) {
        $upcoming_reminders[] = $row;
    }
    
    // Get overdue tasks
    $overdue_tasks_stmt = $task->getOverdueTasks($user_id);
    $overdue_tasks = [];
    while ($row = $overdue_tasks_stmt->fetch(PDO::FETCH_ASSOC)) {
        $overdue_tasks[] = $row;
    }
    
    // Get recent tasks
    $recent_tasks_stmt = $task->readByUser($user_id);
    $recent_tasks = [];
    $count = 0;
    while ($row = $recent_tasks_stmt->fetch(PDO::FETCH_ASSOC) && $count < 5) {
        $recent_tasks[] = $row;
        $count++;
    }
    
    jsonResponse([
        'success' => true,
        'dashboard' => [
            'task_statistics' => $task_stats,
            'upcoming_reminders' => $upcoming_reminders,
            'overdue_tasks' => $overdue_tasks,
            'recent_tasks' => $recent_tasks,
            'summary' => [
                'upcoming_reminders_count' => count($upcoming_reminders),
                'overdue_tasks_count' => count($overdue_tasks),
                'completion_rate' => $task_stats['total_tasks'] > 0 ? 
                    round(($task_stats['completed_tasks'] / $task_stats['total_tasks']) * 100, 2) : 0
            ]
        ]
    ]);
}

function getDashboardTasks() {
    $user_id = $_SESSION['user_id'];
    
    $database = new Database();
    $db = $database->getConnection();
    $task = new Task($db);
    
    // Get tasks by status
    $all_tasks_stmt = $task->readByUser($user_id);
    $tasks_by_status = [
        'pending' => [],
        'in_progress' => [],
        'completed' => []
    ];
    
    $tasks_by_priority = [
        'high' => [],
        'medium' => [],
        'low' => []
    ];
    
    while ($row = $all_tasks_stmt->fetch(PDO::FETCH_ASSOC)) {
        $tasks_by_status[$row['status']][] = $row;
        $tasks_by_priority[$row['priority']][] = $row;
    }
    
    // Get overdue tasks
    $overdue_tasks_stmt = $task->getOverdueTasks($user_id);
    $overdue_tasks = [];
    while ($row = $overdue_tasks_stmt->fetch(PDO::FETCH_ASSOC)) {
        $overdue_tasks[] = $row;
    }
    
    jsonResponse([
        'success' => true,
        'tasks' => [
            'by_status' => $tasks_by_status,
            'by_priority' => $tasks_by_priority,
            'overdue' => $overdue_tasks,
            'counts' => [
                'pending' => count($tasks_by_status['pending']),
                'in_progress' => count($tasks_by_status['in_progress']),
                'completed' => count($tasks_by_status['completed']),
                'high_priority' => count($tasks_by_priority['high']),
                'overdue' => count($overdue_tasks)
            ]
        ]
    ]);
}

function getDashboardReminders() {
    $user_id = $_SESSION['user_id'];
    
    $database = new Database();
    $db = $database->getConnection();
    $reminder = new Reminder($db);
    
    // Get upcoming reminders (next 24 hours)
    $upcoming_stmt = $reminder->getUpcoming($user_id, 24);
    $upcoming = [];
    while ($row = $upcoming_stmt->fetch(PDO::FETCH_ASSOC)) {
        $upcoming[] = $row;
    }
    
    // Get upcoming reminders (next week)
    $upcoming_week_stmt = $reminder->getUpcoming($user_id, 168); // 7 days * 24 hours
    $upcoming_week = [];
    while ($row = $upcoming_week_stmt->fetch(PDO::FETCH_ASSOC)) {
        $upcoming_week[] = $row;
    }
    
    // Get overdue reminders
    $overdue_stmt = $reminder->getOverdue($user_id);
    $overdue = [];
    while ($row = $overdue_stmt->fetch(PDO::FETCH_ASSOC)) {
        $overdue[] = $row;
    }
    
    // Get all user reminders for statistics
    $all_reminders_stmt = $reminder->readByUser($user_id);
    $all_reminders = [];
    $active_count = 0;
    $completed_count = 0;
    $cancelled_count = 0;
    
    while ($row = $all_reminders_stmt->fetch(PDO::FETCH_ASSOC)) {
        $all_reminders[] = $row;
        switch ($row['status']) {
            case 'active':
                $active_count++;
                break;
            case 'completed':
                $completed_count++;
                break;
            case 'cancelled':
                $cancelled_count++;
                break;
        }
    }
    
    jsonResponse([
        'success' => true,
        'reminders' => [
            'upcoming_24h' => $upcoming,
            'upcoming_week' => $upcoming_week,
            'overdue' => $overdue,
            'all' => $all_reminders,
            'counts' => [
                'upcoming_24h' => count($upcoming),
                'upcoming_week' => count($upcoming_week),
                'overdue' => count($overdue),
                'active' => $active_count,
                'completed' => $completed_count,
                'cancelled' => $cancelled_count,
                'total' => count($all_reminders)
            ]
        ]
    ]);
}

function getDashboardStatistics() {
    $user_id = $_SESSION['user_id'];
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Get task statistics
    $task = new Task($db);
    $task_stats = $task->getStatistics($user_id);
    
    // Get activity statistics for the user
    $activity_log = new ActivityLog($db);
    $user_activities_stmt = $activity_log->readByUser($user_id, 100, 0);
    
    $activity_counts = [
        'task_created' => 0,
        'task_updated' => 0,
        'task_deleted' => 0,
        'reminder_created' => 0,
        'reminder_updated' => 0,
        'reminder_deleted' => 0,
        'login' => 0
    ];
    
    $daily_activity = [];
    
    while ($row = $user_activities_stmt->fetch(PDO::FETCH_ASSOC)) {
        $action_key = $row['action'];
        if ($row['entity_type'] !== 'user') {
            $action_key = $row['entity_type'] . '_' . $row['action'];
        }
        
        if (isset($activity_counts[$action_key])) {
            $activity_counts[$action_key]++;
        }
        
        // Group by date for daily activity chart
        $date = date('Y-m-d', strtotime($row['created_at']));
        if (!isset($daily_activity[$date])) {
            $daily_activity[$date] = 0;
        }
        $daily_activity[$date]++;
    }
    
    // Convert daily activity to array format for charts
    $daily_activity_chart = [];
    foreach ($daily_activity as $date => $count) {
        $daily_activity_chart[] = [
            'date' => $date,
            'activity_count' => $count
        ];
    }
    
    // Sort by date
    usort($daily_activity_chart, function($a, $b) {
        return strtotime($a['date']) - strtotime($b['date']);
    });
    
    // Calculate productivity metrics
    $productivity_score = 0;
    if ($task_stats['total_tasks'] > 0) {
        $completion_rate = ($task_stats['completed_tasks'] / $task_stats['total_tasks']) * 100;
        $productivity_score = $completion_rate;
        
        // Bonus for high priority tasks completed
        if ($task_stats['high_priority_pending'] == 0 && $task_stats['completed_tasks'] > 0) {
            $productivity_score += 10;
        }
        
        // Penalty for overdue tasks
        $overdue_tasks_stmt = $task->getOverdueTasks($user_id);
        $overdue_count = 0;
        while ($overdue_tasks_stmt->fetch()) {
            $overdue_count++;
        }
        
        if ($overdue_count > 0) {
            $productivity_score -= ($overdue_count * 5);
        }
        
        $productivity_score = max(0, min(100, $productivity_score));
    }
    
    jsonResponse([
        'success' => true,
        'statistics' => [
            'task_stats' => $task_stats,
            'activity_counts' => $activity_counts,
            'daily_activity_chart' => $daily_activity_chart,
            'productivity_metrics' => [
                'completion_rate' => $task_stats['total_tasks'] > 0 ? 
                    round(($task_stats['completed_tasks'] / $task_stats['total_tasks']) * 100, 2) : 0,
                'productivity_score' => round($productivity_score, 2),
                'tasks_per_day' => count($daily_activity_chart) > 0 ? 
                    round(array_sum(array_column($daily_activity_chart, 'activity_count')) / count($daily_activity_chart), 2) : 0
            ]
        ]
    ]);
}
?>
