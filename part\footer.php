<div class="mt-4"></div>
<nav class="navbar justify-content-center bg-light fixed-bottom m-0 p-0">
  <div class="small fst-italic">
    Copyright © 2022 <a href="https://rektech.uk/">RekTech</a>. All rights reserved.
  </div> 
</nav>

<button id="add-to-home" style="display: none;" onclick="addToHomeScreen()">Add to Home Screen</button>

<script>
  window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;
    document.getElementById('add-to-home').style.display = 'block';
  });

  function addToHomeScreen() {
    deferredPrompt.prompt();
    deferredPrompt.userChoice.then((choiceResult) => {
      if (choiceResult.outcome === 'accepted') {
        console.log('User accepted the A2HS prompt');
      } else {
        console.log('User dismissed the A2HS prompt');
      }
      deferredPrompt = null;
      document.getElementById('add-to-home').style.display = 'none';
    });
  }
</script>

</body>
</html>


