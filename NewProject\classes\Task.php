<?php
/**
 * Task Class
 * Handles CRUD operations for tasks
 */

class Task {
    private $conn;
    private $table_name = "tasks";
    
    public $id;
    public $user_id;
    public $title;
    public $description;
    public $priority;
    public $status;
    public $due_date;
    public $created_at;
    public $updated_at;
    
    public function __construct($db = null) {
        if ($db) {
            $this->conn = $db;
        } else {
            $database = new Database();
            $this->conn = $database->getConnection();
        }
    }
    
    // Create task
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET user_id=:user_id, title=:title, description=:description, 
                      priority=:priority, status=:status, due_date=:due_date";
        
        $stmt = $this->conn->prepare($query);
        
        // Sanitize
        $this->title = validateInput($this->title);
        $this->description = validateInput($this->description);
        
        // Bind values
        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":title", $this->title);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":priority", $this->priority);
        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":due_date", $this->due_date);
        
        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            $this->logActivity('create', 'task', $this->id, [
                'title' => $this->title,
                'priority' => $this->priority
            ]);
            return true;
        }
        
        return false;
    }
    
    // Read user's tasks
    public function readByUser($user_id) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE user_id = :user_id 
                  ORDER BY 
                    CASE priority 
                        WHEN 'high' THEN 1 
                        WHEN 'medium' THEN 2 
                        WHEN 'low' THEN 3 
                    END,
                    due_date ASC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Read all tasks (admin only)
    public function readAll() {
        $query = "SELECT t.*, u.username, u.full_name 
                  FROM " . $this->table_name . " t
                  LEFT JOIN users u ON t.user_id = u.id
                  ORDER BY t.created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Read single task
    public function readOne() {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE id = ? LIMIT 0,1";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($row) {
            $this->user_id = $row['user_id'];
            $this->title = $row['title'];
            $this->description = $row['description'];
            $this->priority = $row['priority'];
            $this->status = $row['status'];
            $this->due_date = $row['due_date'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return true;
        }
        
        return false;
    }
    
    // Update task
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET title=:title, description=:description, priority=:priority, 
                      status=:status, due_date=:due_date 
                  WHERE id=:id";
        
        $stmt = $this->conn->prepare($query);
        
        // Sanitize
        $this->title = validateInput($this->title);
        $this->description = validateInput($this->description);
        
        // Bind values
        $stmt->bindParam(":title", $this->title);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":priority", $this->priority);
        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":due_date", $this->due_date);
        $stmt->bindParam(":id", $this->id);
        
        if ($stmt->execute()) {
            $this->logActivity('update', 'task', $this->id, [
                'title' => $this->title,
                'status' => $this->status
            ]);
            return true;
        }
        
        return false;
    }
    
    // Delete task
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        
        if ($stmt->execute()) {
            $this->logActivity('delete', 'task', $this->id, [
                'title' => $this->title
            ]);
            return true;
        }
        
        return false;
    }
    
    // Get task statistics for user
    public function getStatistics($user_id) {
        $query = "SELECT 
                    COUNT(*) as total_tasks,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_tasks,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_tasks,
                    SUM(CASE WHEN priority = 'high' AND status != 'completed' THEN 1 ELSE 0 END) as high_priority_pending
                  FROM " . $this->table_name . " 
                  WHERE user_id = :user_id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    // Get overdue tasks
    public function getOverdueTasks($user_id) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE user_id = :user_id 
                  AND due_date < NOW() 
                  AND status != 'completed'
                  ORDER BY due_date ASC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Check if user owns this task
    public function isOwner($user_id) {
        if (!$this->readOne()) {
            return false;
        }
        return $this->user_id == $user_id;
    }
    
    // Log activity
    private function logActivity($action, $entity_type, $entity_id, $details = []) {
        $query = "INSERT INTO activity_logs 
                  SET user_id=:user_id, action=:action, entity_type=:entity_type, 
                      entity_id=:entity_id, details=:details, ip_address=:ip_address, 
                      user_agent=:user_agent";
        
        $stmt = $this->conn->prepare($query);
        
        $user_id = $_SESSION['user_id'] ?? null;
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $details_json = json_encode($details);
        
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":action", $action);
        $stmt->bindParam(":entity_type", $entity_type);
        $stmt->bindParam(":entity_id", $entity_id);
        $stmt->bindParam(":details", $details_json);
        $stmt->bindParam(":ip_address", $ip_address);
        $stmt->bindParam(":user_agent", $user_agent);
        
        $stmt->execute();
    }
}
?>
