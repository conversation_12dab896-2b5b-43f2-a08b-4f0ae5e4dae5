// Dashboard JavaScript
const API_BASE = '../api';
let currentUser = null;

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    checkAuth();
    
    // Initialize event listeners
    initializeEventListeners();
    
    // Load dashboard data
    loadDashboardData();
});

// Check if user is authenticated
function checkAuth() {
    const user = localStorage.getItem('user');
    if (!user) {
        window.location.href = 'login.html';
        return;
    }
    
    currentUser = JSON.parse(user);
    document.getElementById('userWelcome').textContent = `Welcome, ${currentUser.full_name}!`;
}

// Initialize event listeners
function initializeEventListeners() {
    // Navigation
    document.querySelectorAll('[data-section]').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            showSection(section);
            
            // Update active nav
            document.querySelectorAll('.nav-link').forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Logout
    document.getElementById('logoutBtn').addEventListener('click', logout);
    
    // Task form
    document.getElementById('saveTaskBtn').addEventListener('click', saveTask);
    
    // Reminder form
    document.getElementById('saveReminderBtn').addEventListener('click', saveReminder);
    
    // Recurring reminder toggle
    document.getElementById('isRecurring').addEventListener('change', function() {
        const recurrenceDiv = document.getElementById('recurrencePatternDiv');
        recurrenceDiv.style.display = this.checked ? 'block' : 'none';
    });
    
    // Task search and filter
    document.getElementById('taskSearch').addEventListener('input', filterTasks);
    document.getElementById('taskFilter').addEventListener('change', filterTasks);
}

// Show specific section
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // Show selected section
    document.getElementById(`${sectionName}-section`).style.display = 'block';
    
    // Update page title
    const titles = {
        'overview': 'Dashboard Overview',
        'tasks': 'My Tasks',
        'reminders': 'My Reminders',
        'profile': 'Profile'
    };
    document.getElementById('pageTitle').textContent = titles[sectionName] || 'Dashboard';
    
    // Load section-specific data
    switch(sectionName) {
        case 'tasks':
            loadTasks();
            break;
        case 'reminders':
            loadReminders();
            break;
        case 'profile':
            loadProfile();
            break;
    }
}

// Load dashboard overview data
async function loadDashboardData() {
    try {
        const response = await fetch(`${API_BASE}/dashboard/overview`);
        const data = await response.json();
        
        if (data.success) {
            updateDashboardStats(data.dashboard);
            displayRecentTasks(data.dashboard.recent_tasks);
            displayUpcomingReminders(data.dashboard.upcoming_reminders);
        }
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showAlert('Error loading dashboard data', 'danger');
    }
}

// Update dashboard statistics
function updateDashboardStats(dashboard) {
    const stats = dashboard.task_statistics;
    document.getElementById('totalTasks').textContent = stats.total_tasks || 0;
    document.getElementById('completedTasks').textContent = stats.completed_tasks || 0;
    document.getElementById('pendingTasks').textContent = stats.pending_tasks || 0;
    document.getElementById('upcomingReminders').textContent = dashboard.summary.upcoming_reminders_count || 0;
}

// Display recent tasks
function displayRecentTasks(tasks) {
    const container = document.getElementById('recentTasks');
    
    if (!tasks || tasks.length === 0) {
        container.innerHTML = '<p class="text-muted">No recent tasks</p>';
        return;
    }
    
    const html = tasks.map(task => `
        <div class="d-flex justify-content-between align-items-center py-2 border-bottom task-priority-${task.priority}">
            <div>
                <h6 class="mb-1">${task.title}</h6>
                <small class="text-muted">${task.priority} priority • ${task.status}</small>
            </div>
            <div>
                <button class="btn btn-sm btn-outline-primary" onclick="editTask(${task.id})">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// Display upcoming reminders
function displayUpcomingReminders(reminders) {
    const container = document.getElementById('upcomingRemindersList');
    
    if (!reminders || reminders.length === 0) {
        container.innerHTML = '<p class="text-muted">No upcoming reminders</p>';
        return;
    }
    
    const html = reminders.map(reminder => `
        <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
            <div>
                <h6 class="mb-1">${reminder.title}</h6>
                <small class="text-muted">${formatDateTime(reminder.reminder_time)}</small>
            </div>
            <div>
                <button class="btn btn-sm btn-outline-warning" onclick="editReminder(${reminder.id})">
                    <i class="fas fa-bell"></i>
                </button>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// Load tasks
async function loadTasks() {
    try {
        const response = await fetch(`${API_BASE}/tasks`);
        const data = await response.json();
        
        if (data.success) {
            displayTasks(data.tasks);
        }
    } catch (error) {
        console.error('Error loading tasks:', error);
        showAlert('Error loading tasks', 'danger');
    }
}

// Display tasks
function displayTasks(tasks) {
    const container = document.getElementById('tasksList');
    
    if (!tasks || tasks.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No tasks found</p>';
        return;
    }
    
    const html = tasks.map(task => `
        <div class="card mb-3 task-priority-${task.priority}">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h5 class="card-title">${task.title}</h5>
                        <p class="card-text">${task.description || 'No description'}</p>
                        <div class="d-flex gap-2 mb-2">
                            <span class="badge bg-${getPriorityColor(task.priority)}">${task.priority}</span>
                            <span class="badge bg-${getStatusColor(task.status)}">${task.status.replace('_', ' ')}</span>
                            ${task.due_date ? `<span class="badge bg-secondary">${formatDateTime(task.due_date)}</span>` : ''}
                        </div>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="editTask(${task.id})">
                                <i class="fas fa-edit me-2"></i>Edit
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="deleteTask(${task.id})">
                                <i class="fas fa-trash me-2"></i>Delete
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// Load reminders
async function loadReminders() {
    try {
        const response = await fetch(`${API_BASE}/reminders`);
        const data = await response.json();
        
        if (data.success) {
            displayReminders(data.reminders);
        }
    } catch (error) {
        console.error('Error loading reminders:', error);
        showAlert('Error loading reminders', 'danger');
    }
}

// Display reminders
function displayReminders(reminders) {
    const container = document.getElementById('remindersList');
    
    if (!reminders || reminders.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No reminders found</p>';
        return;
    }
    
    const html = reminders.map(reminder => `
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h5 class="card-title">${reminder.title}</h5>
                        <p class="card-text">${reminder.description || 'No description'}</p>
                        <div class="d-flex gap-2 mb-2">
                            <span class="badge bg-info">${formatDateTime(reminder.reminder_time)}</span>
                            <span class="badge bg-${getStatusColor(reminder.status)}">${reminder.status}</span>
                            ${reminder.is_recurring ? `<span class="badge bg-warning">${reminder.recurrence_pattern}</span>` : ''}
                            ${reminder.task_title ? `<span class="badge bg-secondary">Task: ${reminder.task_title}</span>` : ''}
                        </div>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="editReminder(${reminder.id})">
                                <i class="fas fa-edit me-2"></i>Edit
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="deleteReminder(${reminder.id})">
                                <i class="fas fa-trash me-2"></i>Delete
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// Load profile
async function loadProfile() {
    try {
        const response = await fetch(`${API_BASE}/auth/profile`);
        const data = await response.json();
        
        if (data.success) {
            const user = data.user;
            document.getElementById('profileUsername').value = user.username;
            document.getElementById('profileEmail').value = user.email;
            document.getElementById('profileFullName').value = user.full_name;
            document.getElementById('profileRole').value = user.role;
            document.getElementById('profileStatus').value = user.status;
            
            // Load profile statistics
            loadProfileStats();
        }
    } catch (error) {
        console.error('Error loading profile:', error);
        showAlert('Error loading profile', 'danger');
    }
}

// Load profile statistics
async function loadProfileStats() {
    try {
        const response = await fetch(`${API_BASE}/dashboard/statistics`);
        const data = await response.json();
        
        if (data.success) {
            const stats = data.statistics;
            const html = `
                <div class="mb-3">
                    <strong>Total Tasks:</strong> ${stats.task_stats.total_tasks || 0}
                </div>
                <div class="mb-3">
                    <strong>Completed Tasks:</strong> ${stats.task_stats.completed_tasks || 0}
                </div>
                <div class="mb-3">
                    <strong>Completion Rate:</strong> ${stats.productivity_metrics.completion_rate || 0}%
                </div>
                <div class="mb-3">
                    <strong>Productivity Score:</strong> ${stats.productivity_metrics.productivity_score || 0}
                </div>
            `;
            document.getElementById('profileStats').innerHTML = html;
        }
    } catch (error) {
        console.error('Error loading profile stats:', error);
    }
}

// Save task
async function saveTask() {
    const taskData = {
        title: document.getElementById('taskTitle').value,
        description: document.getElementById('taskDescription').value,
        priority: document.getElementById('taskPriority').value,
        status: document.getElementById('taskStatus').value,
        due_date: document.getElementById('taskDueDate').value || null
    };
    
    try {
        const response = await fetch(`${API_BASE}/tasks`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(taskData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('Task created successfully!', 'success');
            document.getElementById('taskForm').reset();
            bootstrap.Modal.getInstance(document.getElementById('taskModal')).hide();
            loadDashboardData();
            if (document.getElementById('tasks-section').style.display !== 'none') {
                loadTasks();
            }
        } else {
            showAlert(data.error || 'Failed to create task', 'danger');
        }
    } catch (error) {
        console.error('Error saving task:', error);
        showAlert('Error saving task', 'danger');
    }
}

// Save reminder
async function saveReminder() {
    const reminderData = {
        title: document.getElementById('reminderTitle').value,
        description: document.getElementById('reminderDescription').value,
        reminder_time: document.getElementById('reminderTime').value,
        is_recurring: document.getElementById('isRecurring').checked,
        recurrence_pattern: document.getElementById('isRecurring').checked ? 
            document.getElementById('recurrencePattern').value : null
    };
    
    try {
        const response = await fetch(`${API_BASE}/reminders`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(reminderData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('Reminder created successfully!', 'success');
            document.getElementById('reminderForm').reset();
            document.getElementById('recurrencePatternDiv').style.display = 'none';
            bootstrap.Modal.getInstance(document.getElementById('reminderModal')).hide();
            loadDashboardData();
            if (document.getElementById('reminders-section').style.display !== 'none') {
                loadReminders();
            }
        } else {
            showAlert(data.error || 'Failed to create reminder', 'danger');
        }
    } catch (error) {
        console.error('Error saving reminder:', error);
        showAlert('Error saving reminder', 'danger');
    }
}

// Utility functions
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString();
}

function getPriorityColor(priority) {
    const colors = {
        'high': 'danger',
        'medium': 'warning',
        'low': 'success'
    };
    return colors[priority] || 'secondary';
}

function getStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'in_progress': 'info',
        'completed': 'success',
        'active': 'info',
        'cancelled': 'secondary'
    };
    return colors[status] || 'secondary';
}

function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container');
    alertContainer.innerHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Auto-hide success alerts
    if (type === 'success') {
        setTimeout(() => {
            const alert = alertContainer.querySelector('.alert');
            if (alert) {
                bootstrap.Alert.getInstance(alert).close();
            }
        }, 3000);
    }
}

// Filter tasks
function filterTasks() {
    // This would implement client-side filtering
    // For now, we'll reload tasks from server
    loadTasks();
}

// Edit task (placeholder)
function editTask(taskId) {
    console.log('Edit task:', taskId);
    // Implement edit functionality
}

// Delete task
async function deleteTask(taskId) {
    if (!confirm('Are you sure you want to delete this task?')) {
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE}/tasks/${taskId}`, {
            method: 'DELETE'
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('Task deleted successfully!', 'success');
            loadDashboardData();
            if (document.getElementById('tasks-section').style.display !== 'none') {
                loadTasks();
            }
        } else {
            showAlert(data.error || 'Failed to delete task', 'danger');
        }
    } catch (error) {
        console.error('Error deleting task:', error);
        showAlert('Error deleting task', 'danger');
    }
}

// Edit reminder (placeholder)
function editReminder(reminderId) {
    console.log('Edit reminder:', reminderId);
    // Implement edit functionality
}

// Delete reminder
async function deleteReminder(reminderId) {
    if (!confirm('Are you sure you want to delete this reminder?')) {
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE}/reminders/${reminderId}`, {
            method: 'DELETE'
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('Reminder deleted successfully!', 'success');
            loadDashboardData();
            if (document.getElementById('reminders-section').style.display !== 'none') {
                loadReminders();
            }
        } else {
            showAlert(data.error || 'Failed to delete reminder', 'danger');
        }
    } catch (error) {
        console.error('Error deleting reminder:', error);
        showAlert('Error deleting reminder', 'danger');
    }
}

// Logout
async function logout() {
    try {
        await fetch(`${API_BASE}/auth/logout`, {
            method: 'POST'
        });
    } catch (error) {
        console.error('Logout error:', error);
    } finally {
        localStorage.removeItem('user');
        window.location.href = 'login.html';
    }
}
