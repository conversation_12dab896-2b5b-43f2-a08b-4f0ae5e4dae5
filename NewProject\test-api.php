<?php
/**
 * API Test File
 * Simple test to check if the API is working
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>API Test</h2>";

// Test database connection
echo "<h3>1. Database Connection Test</h3>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "✅ Database connection successful<br>";
        
        // Test if tables exist
        $tables = ['users', 'tasks', 'reminders', 'activity_logs'];
        foreach ($tables as $table) {
            $stmt = $db->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            if ($stmt->rowCount() > 0) {
                echo "✅ Table '$table' exists<br>";
            } else {
                echo "❌ Table '$table' missing<br>";
            }
        }
    } else {
        echo "❌ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test API endpoints
echo "<h3>2. API Endpoint Test</h3>";

// Test auth check endpoint
echo "<h4>Testing auth/check endpoint:</h4>";
$url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/auth/check';
echo "URL: $url<br>";

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Content-Type: application/json',
        'timeout' => 10
    ]
]);

$response = @file_get_contents($url, false, $context);
if ($response !== false) {
    echo "✅ API endpoint accessible<br>";
    echo "Response: " . htmlspecialchars($response) . "<br>";
} else {
    echo "❌ API endpoint not accessible<br>";
    echo "Error: " . error_get_last()['message'] . "<br>";
}

// Test direct file access
echo "<h4>Testing direct file access:</h4>";
if (file_exists('api/auth.php')) {
    echo "✅ auth.php file exists<br>";
} else {
    echo "❌ auth.php file missing<br>";
}

// Test configuration
echo "<h3>3. Configuration Test</h3>";
if (file_exists('config/config.php')) {
    echo "✅ config.php exists<br>";
    require_once 'config/config.php';
    echo "✅ config.php loaded successfully<br>";
} else {
    echo "❌ config.php missing<br>";
}

// Test classes
echo "<h3>4. Classes Test</h3>";
$classes = ['User', 'Task', 'Reminder', 'ActivityLog'];
foreach ($classes as $class) {
    if (file_exists("classes/$class.php")) {
        echo "✅ $class.php exists<br>";
        try {
            require_once "classes/$class.php";
            if (class_exists($class)) {
                echo "✅ $class class loaded<br>";
            } else {
                echo "❌ $class class not found<br>";
            }
        } catch (Exception $e) {
            echo "❌ Error loading $class: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ $class.php missing<br>";
    }
}

echo "<h3>5. Server Information</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Current Directory: " . __DIR__ . "<br>";

echo "<h3>6. Recommendations</h3>";
echo "<ul>";
echo "<li>Make sure you've imported the database schema from database/schema.sql</li>";
echo "<li>Update database credentials in config/database.php</li>";
echo "<li>Ensure your web server supports URL rewriting (.htaccess)</li>";
echo "<li>Check that all files have proper read permissions</li>";
echo "</ul>";
?>
