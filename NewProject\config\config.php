<?php
/**
 * Application Configuration
 * Inspired by PreTest.app bootfile.php but modernized
 */

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define constants
define('BASE_PATH', dirname(__DIR__) . '/');
define('API_PATH', BASE_PATH . 'api/');
define('CLASSES_PATH', BASE_PATH . 'classes/');
define('VIEWS_PATH', BASE_PATH . 'views/');
define('ASSETS_PATH', BASE_PATH . 'assets/');

// Site URL configuration
define('SITE_URL', 'http://localhost/NewProject/');
define('API_URL', SITE_URL . 'api/');

// Include database configuration
require_once BASE_PATH . 'config/database.php';

// Autoload classes
spl_autoload_register(function ($class_name) {
    $file = CLASSES_PATH . $class_name . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// CORS headers for API
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit(0);
}

header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Max-Age: 3600');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// Helper functions
function jsonResponse($data, $status_code = 200) {
    http_response_code($status_code);
    echo json_encode($data);
    exit;
}

function validateInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

function requireAuth() {
    if (!isLoggedIn()) {
        jsonResponse(['error' => 'Authentication required'], 401);
    }
}

function requireAdmin() {
    requireAuth();
    if (!isAdmin()) {
        jsonResponse(['error' => 'Admin access required'], 403);
    }
}
?>
