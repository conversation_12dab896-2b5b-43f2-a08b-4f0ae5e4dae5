<?php 	include("bootfile.php"); 
		if(!empty($_POST)){ 
			$user_ob->add_user($_POST);
			$user_ob->login($_POST['mobile_no'],$_POST['password']);	
		}
?>
<?php include(INC_PATH."header.php"); ?>
<div class="container mt-2">
  <h3>Sign Up</h3>
  <p>Enter Mobile no and password carefully.</p>
  <form action="#" method="POST">
	<div class="row mt-2">
		<div class="col">
			<label for="surname">Surname</label>
			<input type="text" class="form-control" placeholder="Enter Surname" name="surname" required="required" minlength="3" maxlength="20" />
		</div>
		<div class="col">
			<label for="name">Name</label>
			<input type="text" class="form-control" placeholder="Enter Your Name" name="name" required="required" minlength="3" maxlength="20" />
		</div>
	</div>
	<div class="row mt-2">
		<div class="col">
			<label for="sel1" class="form-label">Select Student or Techer</label>
			<select class="form-select" name="type">
				<option selected="selected" value="user">Student</option>
				<option value="author">Teacher</option>
			</select>
		</div>	
	</div>
    <div class="row mt-2">
		<div class="col">
			<label for="mobile">Mobile No</label>
			<input type="number" class="form-control" placeholder="Enter Mobile no" name="mobile_no" required="required" min="6276957193" max="9999999999" />
		</div>
	</div>
    <div class="row mt-2">
		<div class="col">
			<label for="pwd">Password:</label>
			<input type="password" class="form-control" placeholder="Enter password" name="password" required="required" minlength="4" maxlength="20" />
		</div>
		<!--
		<div class="col">
			<label for="pwd">Conform Password:</label>
			<input type="password" class="form-control" placeholder="Enter password" required="required" minlength="4" maxlength="20" />
		</div>  -->
    </div>
	<div class="form-check mt-3 ">
		<div class="col">
			<input type="checkbox" class="form-check-input" required="required" />
			<label for="accept terms and conditions"> I agree to the <a href="">PreTest</a></label>
		</div>
	</div>
	<div class="row m-4">
		<button type="submit" class="btn btn-primary btn-block">Submit</button>
	</div>
  </form>
  <p class="mt-5 h3"><a href="signin.php">User Click here to Login.</a></p>
</div>
<?php include(INC_PATH."footer.php"); ?>