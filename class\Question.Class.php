<?php
class Question extends SQLQuery{
	public $table = 'question';	
	public function add_question($post){
		$query = parent::insert_record($this->table,$post);
		$data = parent::update_query($query);
	}
	public function update_question($post){
		$query = parent::update_record($this->table,$post,$post['id']);
		$data = parent::update_query($query);
	}
	public function delete_question($id){
		$query = parent::delete_record($this->table,$id);
		$result = parent::update_query($query);
	}
	public function view_question_all(){
		$query = parent::select_table($this->table);		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}
	public function select_question_id($id){
		$query = parent::select_record($this->table,$id);		
		$data = parent::nonupdate_query($query);
		$array = parent::create_array($data);
		return $array;
	}	
}
?>